# 🚀 Guida Rapida - Stregatto

Benvenuto in **Stregatto**, la guida completa per imparare **Cheshire Cat AI**! 

## 🎯 Inizia Subito

### 1. 📚 Leggi la Documentazione
```bash
# Inizia qui per capire cos'è Cheshire Cat AI
docs/01-introduzione.md

# Poi segui l'ordine:
docs/02-installazione.md      # Come installare
docs/03-concetti-base.md      # Concetti fondamentali
docs/04-plugin-tools.md       # Creare Tools
docs/05-plugin-hooks.md       # Personalizzare con Hooks
docs/06-plugin-forms.md       # Conversazioni strutturate
docs/07-esempi-avanzati.md    # Progetti complessi
```

### 2. 🧪 Prova gli Esempi
```bash
# Esempi progressivi dal semplice al complesso
examples/01-primo-plugin/         # Il tuo primo plugin
examples/02-tool-semplice/        # Tool utili e pratici
examples/03-hook-personalizzazione/  # Personalizza il comportamento
examples/04-form-interattivo/     # Conversazioni strutturate
examples/05-plugin-completo/      # Progetto complesso
```

### 3. 🔨 Usa i Template
```bash
# Template pronti all'uso
plugins/template-base/         # Template base completo
plugins/calcolatrice/         # Esempio calcolatrice
plugins/meteo/               # Esempio meteo API
plugins/task-manager/        # Esempio task manager
```

### 4. ⚡ Script di Utilità
```bash
# Setup automatico
./scripts/setup.sh

# Crea nuovi plugin
python scripts/create-plugin.py mio_plugin --type basic
```

## 🏃‍♂️ Quick Start (5 minuti)

### Opzione A: Docker (Raccomandato)
```bash
# 1. Clona Cheshire Cat
git clone https://github.com/cheshire-cat-ai/core.git cheshire-cat-ai
cd cheshire-cat-ai

# 2. Avvia con Docker
docker-compose up -d

# 3. Apri il browser
open http://localhost:1865/admin
```

### Opzione B: Python
```bash
# 1. Clona e installa
git clone https://github.com/cheshire-cat-ai/core.git cheshire-cat-ai
cd cheshire-cat-ai
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt

# 2. Avvia
python main.py

# 3. Apri il browser
open http://localhost:1865/admin
```

## 🎓 Percorso di Apprendimento

### 👶 Principiante (1-2 ore)
1. Leggi `docs/01-introduzione.md` - Capire cos'è Cheshire Cat
2. Segui `docs/02-installazione.md` - Installare tutto
3. Prova `examples/01-primo-plugin/` - Il tuo primo plugin
4. Sperimenta con la chat del Cat

### 🧑‍💻 Intermedio (3-5 ore)
5. Studia `docs/03-concetti-base.md` - Architettura e memoria
6. Impara `docs/04-plugin-tools.md` - Creare Tools potenti
7. Prova `examples/02-tool-semplice/` - Tool avanzati
8. Crea il tuo primo tool personalizzato

### 🚀 Avanzato (5-10 ore)
9. Padroneggia `docs/05-plugin-hooks.md` - Personalizzare comportamento
10. Scopri `docs/06-plugin-forms.md` - Conversazioni strutturate
11. Prova `examples/03-hook-personalizzazione/` e `examples/04-form-interattivo/`
12. Studia `docs/07-esempi-avanzati.md` - Progetti complessi

### 🏆 Esperto (10+ ore)
13. Crea plugin complessi combinando Tools, Hooks e Forms
14. Contribuisci alla community con i tuoi plugin
15. Aiuta altri sviluppatori
16. Esplora integrazioni avanzate

## 🛠️ Comandi Utili

### Gestione Plugin
```bash
# Crea nuovo plugin
python scripts/create-plugin.py nome_plugin --type basic

# Copia esempi in Cheshire Cat
cp -r examples/* cheshire-cat-ai/cat/plugins/

# Copia template
cp -r plugins/template-base cheshire-cat-ai/cat/plugins/mio_plugin
```

### Debug e Test
```bash
# Vedi log Docker
docker-compose logs -f cheshire-cat-core

# Vedi log Python
tail -f cheshire-cat-ai/cat/log/cat.log

# Test API
curl -X POST "http://localhost:1865/message" \
  -H "Content-Type: application/json" \
  -d '{"text": "ciao"}'
```

## 📋 Checklist Primo Plugin

- [ ] Ho letto `docs/01-introduzione.md`
- [ ] Ho installato Cheshire Cat
- [ ] Ho testato la chat di base
- [ ] Ho copiato `examples/01-primo-plugin/`
- [ ] Ho modificato `plugin.json` con i miei dati
- [ ] Ho personalizzato almeno un tool
- [ ] Ho testato il plugin nella chat
- [ ] Ho letto la documentazione del mio tool

## 🆘 Risoluzione Problemi

### Problemi Comuni

**Porta 1865 occupata**
```bash
sudo lsof -i :1865
kill -9 <PID>
```

**Plugin non caricato**
- Controlla `plugin.json` sia valido
- Verifica sintassi Python
- Guarda i log per errori

**Errori di import**
```bash
# Installa dipendenze mancanti
pip install nome_libreria

# Aggiungi a requirements.txt del plugin
echo "nome_libreria==versione" >> requirements.txt
```

**LLM non risponde**
- Controlla configurazione API key
- Verifica connessione internet
- Prova con un altro provider

### Dove Chiedere Aiuto

- 💬 **Discord**: [Community Discord](https://discord.gg/bHX5sNFCYU)
- 🐛 **GitHub Issues**: [Segnala bug](https://github.com/cheshire-cat-ai/core/issues)
- 📚 **Documentazione**: [Docs ufficiali](https://cheshire-cat-ai.github.io/docs/)

## 🎉 Prossimi Passi

Una volta che hai padroneggiato le basi:

1. **🌟 Contribuisci**: Condividi i tuoi plugin
2. **📝 Documenta**: Scrivi tutorial per altri
3. **🤝 Aiuta**: Rispondi a domande nella community
4. **🚀 Innova**: Crea integrazioni innovative

---

**Buon divertimento con Cheshire Cat AI!** 🐱✨

*Ricorda: il miglior modo per imparare è sperimentare. Non aver paura di rompere qualcosa - è così che si impara!*
