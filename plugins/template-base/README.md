# 📋 Template Base Plugin

Questo è un template base che puoi usare come punto di partenza per i tuoi plugin.

## 🎯 Cosa Include

- **Struttura base** del plugin
- **Tool di esempio** con gestione errori
- **Hook di esempio** per personalizzazione
- **Documentazione completa**
- **Best practices** integrate

## 📁 Struttura File

```
template-base/
├── plugin.json          # Metadati del plugin
├── main.py             # Codice principale
├── utils.py            # Funzioni di utilità
├── README.md           # Questa documentazione
└── requirements.txt    # Dipendenze (se necessarie)
```

## 🚀 Come Usare Questo Template

1. **Copia la cartella** e rinominala con il nome del tuo plugin
2. **Modifica plugin.json** con i tuoi dati
3. **Personalizza main.py** con la tua logica
4. **Aggiorna README.md** con la tua documentazione
5. **Testa il plugin** prima di distribuirlo

## 🔧 Personalizzazione

### 1. Modifica plugin.json
```json
{
    "name": "Il Tuo Plugin",
    "description": "Descrizione del tuo plugin",
    "author": "Il Tuo Nome",
    "version": "1.0.0",
    "tags": ["i", "tuoi", "tag"]
}
```

### 2. Personalizza i Tool
Modifica la funzione `template_tool` in `main.py`:
- Cambia il nome della funzione
- Aggiorna la documentazione
- Implementa la tua logica

### 3. Personalizza gli Hook
Modifica gli hook in `main.py`:
- Cambia il comportamento del Cat
- Aggiungi nuove personalizzazioni
- Rimuovi hook non necessari

## 📚 Esempi di Personalizzazione

### Tool Personalizzato
```python
@tool
def mio_tool_personalizzato(input, cat):
    """Il mio tool fantastico."""
    
    # La tua logica qui
    result = process_input(input)
    
    return f"Risultato: {result}"
```

### Hook Personalizzato
```python
@hook
def before_cat_sends_message(message, cat):
    """Personalizza i messaggi."""
    
    # Aggiungi la tua personalizzazione
    message["content"] = customize_message(message["content"])
    
    return message
```

## 🧪 Testing

Prima di distribuire il tuo plugin:

1. **Testa tutti i tool** con input diversi
2. **Verifica gli hook** funzionino correttamente
3. **Controlla la gestione errori**
4. **Valida la documentazione**

## 💡 Best Practices

- ✅ **Gestisci sempre gli errori**
- ✅ **Documenta tutto chiaramente**
- ✅ **Usa nomi descrittivi**
- ✅ **Valida gli input utente**
- ✅ **Mantieni il codice pulito**
- ✅ **Testa prima di distribuire**

## 🎯 Prossimi Passi

1. Studia gli esempi in `examples/`
2. Leggi la documentazione in `docs/`
3. Unisciti alla community Discord
4. Condividi il tuo plugin!

---

**Buona programmazione!** 🚀
