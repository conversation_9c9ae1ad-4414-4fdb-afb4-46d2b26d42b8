"""
🐱 Il Mio Primo Plugin per Cheshire Cat AI

Questo plugin dimostra le funzionalità base:
- Tool semplici
- Gestione input/output
- Accesso all'oggetto cat
"""

from cat.mad_hatter.decorators import tool
from datetime import datetime
import random

@tool
def saluta_utente(input, cat):
    """Saluta l'utente in modo amichevole.
    
    Risponde a saluti come: "ciao", "salve", "buongiorno", ecc.
    """
    
    # Lista di saluti casuali
    saluti = [
        "Ciao! 😸 Sono il tuo Cheshire Cat personale!",
        "Salve! 🐱 Come posso aiutarti oggi?",
        "Buongiorno! ☀️ Che bella giornata per chattare!",
        "Ehi! 👋 Pronto per qualche magia felina?",
        "Ciao ciao! 🎭 Il tuo gatto del Cheshire è qui!"
    ]
    
    # Scegli un saluto casuale
    saluto = random.choice(saluti)
    
    # Aggiungi informazioni personalizzate se disponibili
    if hasattr(cat, 'user_id') and cat.user_id:
        saluto += f"\n\nTi riconosco, sei l'utente {cat.user_id}! 🔍"
    
    # Aggiungi suggerimenti
    saluto += "\n\n💡 **Cosa posso fare per te:**"
    saluto += "\n- Dimmi che ore sono"
    saluto += "\n- Raccontami una barzelletta"
    saluto += "\n- Dimmi qualcosa di te"
    
    return saluto

@tool
def che_ore_sono(input, cat):
    """Dice che ore sono adesso.
    
    Risponde a domande come: "che ore sono?", "dimmi l'ora", "orologio"
    """
    
    # Ottieni l'ora attuale
    now = datetime.now()
    
    # Formatta l'ora in modo carino
    ora_formattata = now.strftime("%H:%M")
    data_formattata = now.strftime("%d/%m/%Y")
    giorno_settimana = now.strftime("%A")
    
    # Traduci il giorno in italiano
    giorni_ita = {
        'Monday': 'Lunedì',
        'Tuesday': 'Martedì', 
        'Wednesday': 'Mercoledì',
        'Thursday': 'Giovedì',
        'Friday': 'Venerdì',
        'Saturday': 'Sabato',
        'Sunday': 'Domenica'
    }
    
    giorno_ita = giorni_ita.get(giorno_settimana, giorno_settimana)
    
    # Aggiungi un commento basato sull'ora
    ora_num = now.hour
    if 5 <= ora_num < 12:
        commento = "Buongiorno! ☀️"
    elif 12 <= ora_num < 18:
        commento = "Buon pomeriggio! 🌤️"
    elif 18 <= ora_num < 22:
        commento = "Buonasera! 🌅"
    else:
        commento = "Buonanotte! 🌙"
    
    return f"""🕐 **Ora attuale**: {ora_formattata}
📅 **Data**: {giorno_ita}, {data_formattata}

{commento}"""

@tool
def racconta_barzelletta(input, cat):
    """Racconta una barzelletta divertente.
    
    Risponde a richieste come: "raccontami una barzelletta", "fammi ridere", "barzelletta"
    """
    
    # Lista di barzellette a tema gatti e AI
    barzellette = [
        {
            "setup": "Perché i gatti sono bravi programmatori?",
            "punchline": "Perché sanno sempre dove mettere i punti e virgola! 😸"
        },
        {
            "setup": "Cosa dice un gatto quando vede un computer?",
            "punchline": "Miao-crosoft! 💻🐱"
        },
        {
            "setup": "Perché il Cheshire Cat è il miglior assistente AI?",
            "punchline": "Perché sa sempre come sparire quando non serve! 🎭"
        },
        {
            "setup": "Qual è il linguaggio di programmazione preferito dei gatti?",
            "punchline": "Python... perché odiano i topi! 🐍🐭"
        },
        {
            "setup": "Cosa fa un gatto quando il WiFi non funziona?",
            "punchline": "Prova a riavviare il miao-dem! 📡😹"
        },
        {
            "setup": "Perché i gatti non usano mai Google?",
            "punchline": "Perché preferiscono Yahoo! (Ya-hoo = Miao-hoo) 🔍"
        }
    ]
    
    # Scegli una barzelletta casuale
    barzelletta = random.choice(barzellette)
    
    # Salva nella working memory per statistiche
    if 'barzellette_raccontate' not in cat.working_memory:
        cat.working_memory['barzellette_raccontate'] = 0
    cat.working_memory['barzellette_raccontate'] += 1
    
    return f"""😹 **Barzelletta #{cat.working_memory['barzellette_raccontate']}**

{barzelletta['setup']}

...

{barzelletta['punchline']}

---
🎭 Spero ti sia piaciuta! Chiedimene un'altra quando vuoi!"""

@tool
def info_plugin(input, cat):
    """Fornisce informazioni su questo plugin.
    
    Risponde a domande come: "dimmi qualcosa di te", "info plugin", "cosa sai fare"
    """
    
    # Statistiche dalla working memory
    barzellette_totali = cat.working_memory.get('barzellette_raccontate', 0)
    
    return f"""🐱 **Il Mio Primo Plugin - Info**

📝 **Descrizione**: Sono un plugin di esempio per imparare Cheshire Cat AI!

🛠️ **Cosa so fare**:
- ✅ Salutare gli utenti
- ⏰ Dire che ore sono
- 😹 Raccontare barzellette
- ℹ️ Fornire informazioni su me stesso

📊 **Statistiche sessione**:
- 🎭 Barzellette raccontate: {barzellette_totali}
- 👤 ID utente: {getattr(cat, 'user_id', 'Sconosciuto')}

🎯 **Scopo**: Questo plugin è perfetto per imparare le basi!

💡 **Prossimo passo**: Prova gli esempi più avanzati nella cartella `examples/`!

---
🔧 **Per sviluppatori**: Guarda il codice in `primo_plugin.py` per capire come funziona!"""

@tool
def test_working_memory(input, cat):
    """Dimostra come usare la working memory del Cat.
    
    Salva e recupera informazioni durante la sessione.
    """
    
    # Inizializza contatore se non esiste
    if 'test_counter' not in cat.working_memory:
        cat.working_memory['test_counter'] = 0
    
    # Incrementa contatore
    cat.working_memory['test_counter'] += 1
    counter = cat.working_memory['test_counter']
    
    # Salva timestamp dell'ultima chiamata
    cat.working_memory['last_test_time'] = datetime.now().isoformat()
    
    # Salva l'input dell'utente
    if 'user_inputs' not in cat.working_memory:
        cat.working_memory['user_inputs'] = []
    cat.working_memory['user_inputs'].append(input)
    
    return f"""🧠 **Test Working Memory**

🔢 **Chiamate a questo tool**: {counter}
⏰ **Ultima chiamata**: {cat.working_memory['last_test_time']}
💬 **Il tuo input**: "{input}"
📝 **Tutti i tuoi input**: {cat.working_memory['user_inputs']}

---
💡 **Spiegazione**: La working memory mantiene i dati durante la sessione.
Quando riavvii il Cat, questi dati vengono persi (a differenza della memoria episodica)."""
