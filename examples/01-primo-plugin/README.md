# 🐱 Il Tuo Primo Plugin

Questo è il plugin più semplice possibile per iniziare con Cheshire Cat AI.

## 🎯 Cosa Fa

- Saluta l'utente
- Dice che ore sono
- Racconta una barzelletta
- Mostra come funzionano i tool base

## 📁 File del Plugin

- `plugin.json` - Metadati del plugin
- `primo_plugin.py` - Codice principale con i tool

## 🚀 Come Installare

1. Copia la cartella `01-primo-plugin` in `cat/plugins/`
2. Riavvia il Cheshire Cat
3. Il plugin sarà automaticamente attivo

## 🧪 Come Testare

Prova questi comandi nella chat:

- "Ciao!" → Il Cat ti saluterà
- "Che ore sono?" → Ti dirà l'ora attuale  
- "Raccontami una barzelletta" → Ti farà ridere
- "Dimmi qualcosa di te" → Ti parlerà del plugin

## 📚 Cosa Impari

- Come creare un plugin base
- Come usare il decoratore `@tool`
- Come gestire input e output
- Come accedere all'oggetto `cat`

## 🔧 Personalizzazione

Prova a modificare:

1. **Aggiungi nuovi saluti** in `saluta_utente()`
2. **Cambia le barzellette** in `racconta_barzelletta()`
3. **Aggiungi un nuovo tool** seguendo gli esempi

## 🎯 Prossimo Passo

Quando hai capito questo esempio, vai a `02-tool-semplice/` per imparare tool più avanzati!
