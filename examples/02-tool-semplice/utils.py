"""
Funzioni di utilità per i tool semplici
"""

import re
import math
from typing import Dict, List, Tuple

def safe_eval(expression: str) -> float:
    """
    Valuta un'espressione matematica in modo sicuro.
    
    Args:
        expression: Espressione matematica come stringa
        
    Returns:
        Risultato del calcolo
        
    Raises:
        ValueError: Se l'espressione non è valida
    """
    
    # Lista di funzioni matematiche permesse
    allowed_functions = {
        'sin': math.sin,
        'cos': math.cos,
        'tan': math.tan,
        'sqrt': math.sqrt,
        'log': math.log,
        'log10': math.log10,
        'exp': math.exp,
        'abs': abs,
        'round': round,
        'pi': math.pi,
        'e': math.e
    }
    
    # Sostituisci le funzioni nell'espressione
    for func_name, func in allowed_functions.items():
        if func_name in expression:
            # Per le costanti
            if func_name in ['pi', 'e']:
                expression = expression.replace(func_name, str(func))
            else:
                # Per le funzioni, aggiungi math. prefix
                expression = expression.replace(func_name, f'math.{func_name}')
    
    # Lista di caratteri permessi (numeri, operatori, parentesi, punto decimale)
    allowed_chars = set('0123456789+-*/().math sincotagqrtlogexpabsroundpi ')
    
    # Controlla caratteri non permessi
    if not all(c in allowed_chars for c in expression.lower()):
        raise ValueError("Caratteri non permessi nell'espressione")
    
    # Valuta l'espressione
    try:
        # Usa un namespace limitato per sicurezza
        namespace = {"__builtins__": {}, "math": math}
        result = eval(expression, namespace)
        return float(result)
    except Exception as e:
        raise ValueError(f"Errore nel calcolo: {str(e)}")

def convert_units(value: float, from_unit: str, to_unit: str) -> Tuple[float, str]:
    """
    Converte tra diverse unità di misura.
    
    Args:
        value: Valore da convertire
        from_unit: Unità di partenza
        to_unit: Unità di destinazione
        
    Returns:
        Tupla con (valore_convertito, descrizione_conversione)
    """
    
    # Dizionario delle conversioni (tutto convertito in unità base)
    conversions = {
        # Lunghezza (metro come base)
        'mm': 0.001,
        'cm': 0.01,
        'm': 1.0,
        'km': 1000.0,
        'inch': 0.0254,
        'ft': 0.3048,
        'yard': 0.9144,
        'mile': 1609.34,
        
        # Peso (grammo come base)
        'mg': 0.001,
        'g': 1.0,
        'kg': 1000.0,
        'oz': 28.3495,
        'lb': 453.592,
        
        # Temperatura (gestita separatamente)
        'celsius': 'temp',
        'fahrenheit': 'temp',
        'kelvin': 'temp',
        
        # Volume (litro come base)
        'ml': 0.001,
        'l': 1.0,
        'gal': 3.78541,
        'qt': 0.946353,
        'pt': 0.473176,
        'cup': 0.236588,
    }
    
    from_unit = from_unit.lower()
    to_unit = to_unit.lower()
    
    # Gestione temperatura
    if from_unit in ['celsius', 'fahrenheit', 'kelvin'] or to_unit in ['celsius', 'fahrenheit', 'kelvin']:
        return convert_temperature(value, from_unit, to_unit)
    
    # Controlla se le unità esistono
    if from_unit not in conversions or to_unit not in conversions:
        raise ValueError(f"Unità non supportate: {from_unit} -> {to_unit}")
    
    # Controlla se sono dello stesso tipo (stesso fattore di conversione)
    from_factor = conversions[from_unit]
    to_factor = conversions[to_unit]
    
    # Converti alla unità base, poi alla unità target
    base_value = value * from_factor
    result = base_value / to_factor
    
    description = f"{value} {from_unit} = {result:.6f} {to_unit}"
    
    return result, description

def convert_temperature(value: float, from_unit: str, to_unit: str) -> Tuple[float, str]:
    """Converte temperature tra Celsius, Fahrenheit e Kelvin."""
    
    # Converti tutto a Celsius prima
    if from_unit == 'fahrenheit':
        celsius = (value - 32) * 5/9
    elif from_unit == 'kelvin':
        celsius = value - 273.15
    else:  # celsius
        celsius = value
    
    # Converti da Celsius alla unità target
    if to_unit == 'fahrenheit':
        result = celsius * 9/5 + 32
    elif to_unit == 'kelvin':
        result = celsius + 273.15
    else:  # celsius
        result = celsius
    
    description = f"{value}° {from_unit.title()} = {result:.2f}° {to_unit.title()}"
    
    return result, description

def analyze_text(text: str) -> Dict:
    """
    Analizza un testo e restituisce statistiche.
    
    Args:
        text: Testo da analizzare
        
    Returns:
        Dizionario con le statistiche
    """
    
    # Statistiche base
    char_count = len(text)
    char_count_no_spaces = len(text.replace(' ', ''))
    word_count = len(text.split())
    
    # Conta frasi (approssimativo)
    sentence_endings = ['.', '!', '?']
    sentence_count = sum(text.count(ending) for ending in sentence_endings)
    sentence_count = max(1, sentence_count)  # Almeno 1 frase
    
    # Conta paragrafi
    paragraph_count = len([p for p in text.split('\n') if p.strip()])
    paragraph_count = max(1, paragraph_count)
    
    # Parole uniche
    words = re.findall(r'\b\w+\b', text.lower())
    unique_words = set(words)
    
    # Parola più lunga
    longest_word = max(words, key=len) if words else ""
    
    # Frequenza parole (top 5)
    word_freq = {}
    for word in words:
        if len(word) > 2:  # Ignora parole troppo corte
            word_freq[word] = word_freq.get(word, 0) + 1
    
    top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]
    
    # Calcola medie
    avg_words_per_sentence = word_count / sentence_count
    avg_chars_per_word = char_count_no_spaces / max(1, word_count)
    
    return {
        'caratteri_totali': char_count,
        'caratteri_senza_spazi': char_count_no_spaces,
        'parole_totali': word_count,
        'parole_uniche': len(unique_words),
        'frasi': sentence_count,
        'paragrafi': paragraph_count,
        'parola_piu_lunga': longest_word,
        'lunghezza_parola_piu_lunga': len(longest_word),
        'media_parole_per_frase': round(avg_words_per_sentence, 2),
        'media_caratteri_per_parola': round(avg_chars_per_word, 2),
        'parole_piu_frequenti': top_words,
        'diversita_lessicale': round(len(unique_words) / max(1, word_count), 3)
    }

def validate_url(url: str) -> bool:
    """
    Valida se una stringa è un URL valido.
    
    Args:
        url: URL da validare
        
    Returns:
        True se l'URL è valido
    """
    
    url_pattern = re.compile(
        r'^https?://'  # http:// o https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # dominio
        r'localhost|'  # localhost
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
        r'(?::\d+)?'  # porta opzionale
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None
