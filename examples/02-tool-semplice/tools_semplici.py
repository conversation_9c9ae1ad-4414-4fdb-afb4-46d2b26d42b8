"""
🧮 Tool Semplici ma Potenti per Cheshire Cat AI

Questo plugin dimostra tool più avanzati e utili:
- Calcolatrice matematica avanzata
- Generatore password sicure
- Convertitore unità di misura
- Analizzatore di testo
- Generatore QR Code
"""

from cat.mad_hatter.decorators import tool
import random
import string
import secrets
import base64
from io import BytesIO
from .utils import safe_eval, convert_units, analyze_text, validate_url

# Importa QR code (opzionale)
try:
    import qrcode
    QR_AVAILABLE = True
except ImportError:
    QR_AVAILABLE = False

@tool
def calcolatrice_avanzata(expression, cat):
    """Calcolatrice matematica avanzata.
    
    Supporta operazioni base (+, -, *, /) e funzioni avanzate:
    - sin, cos, tan (in radianti)
    - sqrt (radice quadrata)
    - log, log10 (logaritmi)
    - exp (esponenziale)
    - pi, e (costanti)
    
    Esempi:
    - "2 + 3 * 4"
    - "sqrt(16)"
    - "sin(pi/2)"
    - "log(e)"
    """
    
    try:
        # Pulisci l'input
        expression = expression.strip()
        
        if not expression:
            return "❌ Inserisci un'espressione matematica da calcolare."
        
        # Calcola il risultato
        result = safe_eval(expression)
        
        # Formatta il risultato
        if result == int(result):
            result_str = str(int(result))
        else:
            result_str = f"{result:.6f}".rstrip('0').rstrip('.')
        
        # Salva nella cronologia
        if 'calc_history' not in cat.working_memory:
            cat.working_memory['calc_history'] = []
        
        cat.working_memory['calc_history'].append({
            'expression': expression,
            'result': result
        })
        
        # Mantieni solo le ultime 10 operazioni
        if len(cat.working_memory['calc_history']) > 10:
            cat.working_memory['calc_history'] = cat.working_memory['calc_history'][-10:]
        
        return f"""🧮 **Calcolatrice Avanzata**

📝 **Espressione**: `{expression}`
✅ **Risultato**: **{result_str}**

---
💡 **Funzioni disponibili**: sin, cos, tan, sqrt, log, log10, exp, pi, e
📚 **Cronologia**: {len(cat.working_memory['calc_history'])} calcoli salvati"""
        
    except ValueError as e:
        return f"❌ **Errore di calcolo**: {str(e)}\n\n💡 **Suggerimento**: Controlla la sintassi dell'espressione."
    except Exception as e:
        return f"❌ **Errore imprevisto**: {str(e)}"

@tool
def genera_password(specifiche, cat):
    """Genera password sicure personalizzabili.
    
    Parametri (separati da virgola):
    - lunghezza: numero di caratteri (default: 12)
    - maiuscole: true/false (default: true)
    - minuscole: true/false (default: true)
    - numeri: true/false (default: true)
    - simboli: true/false (default: true)
    
    Esempi:
    - "16" (password di 16 caratteri)
    - "12, simboli=false" (senza simboli)
    - "20, maiuscole=false, simboli=false"
    """
    
    try:
        # Parametri default
        length = 12
        use_uppercase = True
        use_lowercase = True
        use_digits = True
        use_symbols = True
        
        # Parse parametri se forniti
        if specifiche and specifiche.strip():
            params = [p.strip() for p in specifiche.split(',')]
            
            for param in params:
                if param.isdigit():
                    length = int(param)
                elif '=' in param:
                    key, value = param.split('=', 1)
                    key = key.strip().lower()
                    value = value.strip().lower()
                    
                    bool_value = value in ['true', 'si', 'yes', '1']
                    
                    if key in ['maiuscole', 'uppercase']:
                        use_uppercase = bool_value
                    elif key in ['minuscole', 'lowercase']:
                        use_lowercase = bool_value
                    elif key in ['numeri', 'digits', 'numbers']:
                        use_digits = bool_value
                    elif key in ['simboli', 'symbols']:
                        use_symbols = bool_value
        
        # Validazione
        if length < 4:
            return "❌ La password deve essere di almeno 4 caratteri."
        if length > 128:
            return "❌ La password non può superare i 128 caratteri."
        
        if not any([use_uppercase, use_lowercase, use_digits, use_symbols]):
            return "❌ Devi abilitare almeno un tipo di carattere."
        
        # Costruisci il set di caratteri
        chars = ""
        if use_lowercase:
            chars += string.ascii_lowercase
        if use_uppercase:
            chars += string.ascii_uppercase
        if use_digits:
            chars += string.digits
        if use_symbols:
            chars += "!@#$%^&*()_+-=[]{}|;:,.<>?"
        
        # Genera password sicura
        password = ''.join(secrets.choice(chars) for _ in range(length))
        
        # Calcola forza password (approssimativa)
        charset_size = len(chars)
        entropy = length * (charset_size.bit_length() - 1)
        
        if entropy < 30:
            strength = "🔴 Debole"
        elif entropy < 50:
            strength = "🟡 Media"
        elif entropy < 70:
            strength = "🟢 Forte"
        else:
            strength = "🔵 Molto Forte"
        
        # Salva statistiche
        if 'passwords_generated' not in cat.working_memory:
            cat.working_memory['passwords_generated'] = 0
        cat.working_memory['passwords_generated'] += 1
        
        return f"""🔐 **Password Generata**

🔑 **Password**: `{password}`

📊 **Caratteristiche**:
- 📏 Lunghezza: {length} caratteri
- 🔤 Maiuscole: {'✅' if use_uppercase else '❌'}
- 🔡 Minuscole: {'✅' if use_lowercase else '❌'}
- 🔢 Numeri: {'✅' if use_digits else '❌'}
- 🔣 Simboli: {'✅' if use_symbols else '❌'}

💪 **Forza**: {strength} (Entropia: ~{entropy} bit)

---
⚠️ **Importante**: Salva la password in un posto sicuro!
📈 **Statistiche**: {cat.working_memory['passwords_generated']} password generate in questa sessione"""
        
    except Exception as e:
        return f"❌ **Errore nella generazione**: {str(e)}"

@tool
def converti_unita(conversione, cat):
    """Convertitore di unità di misura.
    
    Formato: "valore unità_partenza in unità_destinazione"
    
    Unità supportate:
    - Lunghezza: mm, cm, m, km, inch, ft, yard, mile
    - Peso: mg, g, kg, oz, lb
    - Temperatura: celsius, fahrenheit, kelvin
    - Volume: ml, l, gal, qt, pt, cup
    
    Esempi:
    - "100 km in mile"
    - "25 celsius in fahrenheit"
    - "1 kg in lb"
    """
    
    try:
        # Parse input
        conversione = conversione.strip().lower()
        
        # Pattern: "numero unità in unità"
        import re
        pattern = r'(\d+(?:\.\d+)?)\s*(\w+)\s+(?:in|to|a)\s+(\w+)'
        match = re.match(pattern, conversione)
        
        if not match:
            return """❌ **Formato non valido**

📝 **Formato corretto**: "valore unità_partenza in unità_destinazione"

📚 **Esempi**:
- "100 km in mile"
- "25 celsius in fahrenheit"  
- "1 kg in lb"
- "500 ml in cup" """
        
        value = float(match.group(1))
        from_unit = match.group(2)
        to_unit = match.group(3)
        
        # Esegui conversione
        result, description = convert_units(value, from_unit, to_unit)
        
        # Salva nella cronologia
        if 'conversion_history' not in cat.working_memory:
            cat.working_memory['conversion_history'] = []
        
        cat.working_memory['conversion_history'].append({
            'input': conversione,
            'result': description
        })
        
        # Mantieni solo le ultime 10 conversioni
        if len(cat.working_memory['conversion_history']) > 10:
            cat.working_memory['conversion_history'] = cat.working_memory['conversion_history'][-10:]
        
        return f"""🔄 **Conversione Unità**

📐 **Conversione**: {description}
✅ **Risultato**: **{result:.6f} {to_unit}**

---
📚 **Cronologia**: {len(cat.working_memory['conversion_history'])} conversioni salvate
💡 **Suggerimento**: Prova "lista unità" per vedere tutte le unità supportate"""
        
    except ValueError as e:
        return f"❌ **Errore di conversione**: {str(e)}"
    except Exception as e:
        return f"❌ **Errore imprevisto**: {str(e)}"

@tool
def analizza_testo(testo, cat):
    """Analizza un testo e fornisce statistiche dettagliate.
    
    Fornisce informazioni su:
    - Conteggio caratteri, parole, frasi, paragrafi
    - Parole uniche e diversità lessicale
    - Parole più frequenti
    - Lunghezza media delle parole
    - E altro ancora...
    
    Input: il testo da analizzare
    """
    
    try:
        if not testo or not testo.strip():
            return "❌ Fornisci un testo da analizzare."
        
        # Analizza il testo
        stats = analyze_text(testo)
        
        # Formatta le parole più frequenti
        top_words_str = ""
        for word, count in stats['parole_piu_frequenti']:
            top_words_str += f"  • **{word}**: {count} volte\n"
        
        if not top_words_str:
            top_words_str = "  • Nessuna parola significativa trovata\n"
        
        # Valutazione complessità
        if stats['media_caratteri_per_parola'] > 6:
            complexity = "🔴 Complesso"
        elif stats['media_caratteri_per_parola'] > 4:
            complexity = "🟡 Medio"
        else:
            complexity = "🟢 Semplice"
        
        return f"""📊 **Analisi Testo Completa**

📝 **Statistiche Base**:
- 📄 Caratteri totali: {stats['caratteri_totali']}
- 🔤 Caratteri (senza spazi): {stats['caratteri_senza_spazi']}
- 📝 Parole totali: {stats['parole_totali']}
- 🆕 Parole uniche: {stats['parole_uniche']}
- 📋 Frasi: {stats['frasi']}
- 📄 Paragrafi: {stats['paragrafi']}

🔍 **Analisi Avanzata**:
- 📏 Parola più lunga: "{stats['parola_piu_lunga']}" ({stats['lunghezza_parola_piu_lunga']} caratteri)
- 📊 Media parole per frase: {stats['media_parole_per_frase']}
- 📈 Media caratteri per parola: {stats['media_caratteri_per_parola']}
- 🎯 Diversità lessicale: {stats['diversita_lessicale']} (0-1)
- 🧠 Complessità: {complexity}

🏆 **Parole Più Frequenti**:
{top_words_str}
---
💡 **Suggerimento**: Una diversità lessicale alta (>0.7) indica un vocabolario ricco!"""
        
    except Exception as e:
        return f"❌ **Errore nell'analisi**: {str(e)}"

@tool
def genera_qr_code(contenuto, cat):
    """Genera un codice QR per testo o URL.
    
    Il QR code viene salvato come immagine base64 che può essere visualizzata.
    
    Esempi:
    - "https://example.com"
    - "Il mio testo segreto"
    - "WIFI:T:WPA;S:NomeRete;P:Password;;"
    """
    
    if not QR_AVAILABLE:
        return """❌ **QR Code non disponibile**

Per usare questa funzione, installa la libreria qrcode:
```bash
pip install qrcode[pil]
```

Poi riavvia il Cheshire Cat."""
    
    try:
        if not contenuto or not contenuto.strip():
            return "❌ Fornisci il contenuto per il QR code."
        
        # Crea il QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        
        qr.add_data(contenuto)
        qr.make(fit=True)
        
        # Crea l'immagine
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Converti in base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        # Determina il tipo di contenuto
        if validate_url(contenuto):
            content_type = "🌐 URL"
        elif contenuto.startswith("WIFI:"):
            content_type = "📶 WiFi"
        elif "@" in contenuto and "." in contenuto:
            content_type = "📧 Email"
        else:
            content_type = "📝 Testo"
        
        # Salva statistiche
        if 'qr_codes_generated' not in cat.working_memory:
            cat.working_memory['qr_codes_generated'] = 0
        cat.working_memory['qr_codes_generated'] += 1
        
        return f"""📱 **QR Code Generato**

🏷️ **Tipo**: {content_type}
📝 **Contenuto**: {contenuto[:100]}{'...' if len(contenuto) > 100 else ''}
📏 **Lunghezza**: {len(contenuto)} caratteri

🖼️ **Immagine QR Code**:
![QR Code](data:image/png;base64,{img_base64})

---
📊 **Statistiche**: {cat.working_memory['qr_codes_generated']} QR code generati in questa sessione
💡 **Suggerimento**: Scansiona il QR code con il tuo smartphone!"""
        
    except Exception as e:
        return f"❌ **Errore nella generazione QR**: {str(e)}"
