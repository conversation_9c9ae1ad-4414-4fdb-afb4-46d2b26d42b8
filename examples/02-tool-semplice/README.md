# 🧮 Tool Semplici ma Potenti

Questo esempio mostra come creare tool più utili e pratici per il tuo Cheshire Cat.

## 🎯 Cosa Fa

- **Calcolatrice avanzata** - Calcoli matematici complessi
- **Generatore password** - Password sicure personalizzabili
- **Convertitore unità** - Conversioni tra diverse unità di misura
- **Analizzatore testo** - Statistiche su testi
- **QR Code generator** - Crea codici QR

## 📁 File del Plugin

- `plugin.json` - Metadati del plugin
- `tools_semplici.py` - Tool principali
- `utils.py` - Funzioni di utilità
- `requirements.txt` - Dipendenze Python

## 🚀 Come Installare

1. Copia la cartella `02-tool-semplice` in `cat/plugins/`
2. Installa le dipendenze: `pip install qrcode[pil]`
3. Riavvia il Cheshire Cat

## 🧪 Come Testare

### Calcolatrice
- "Calcola 2 + 3 * 4"
- "Quanto fa sqrt(16)?"
- "sin(30) in gradi"

### Password Generator
- "Genera una password"
- "Password di 16 caratteri"
- "Password sicura senza simboli"

### Convertitore
- "Converti 100 km in miglia"
- "Quanti gradi Fahrenheit sono 25 Celsius?"
- "1 metro in centimetri"

### Analizzatore Testo
- "Analizza questo testo: Lorem ipsum dolor sit amet..."

### QR Code
- "Crea QR code per https://example.com"

## 📚 Cosa Impari

- Tool con logica complessa
- Gestione errori robusta
- Validazione input
- Uso di librerie esterne
- Formattazione output professionale

## 🔧 Personalizzazione

1. **Aggiungi nuove operazioni** alla calcolatrice
2. **Personalizza le password** con regole specifiche
3. **Aggiungi nuove conversioni** di unità
4. **Estendi l'analisi testo** con sentiment analysis

## 🎯 Prossimo Passo

Vai a `03-hook-personalizzazione/` per imparare a personalizzare il comportamento del Cat!
