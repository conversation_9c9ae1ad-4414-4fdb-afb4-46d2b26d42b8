#!/usr/bin/env python3
"""
🔨 Plugin Creator per Cheshire Cat AI

Questo script ti aiuta a creare rapidamente nuovi plugin
con template predefiniti e best practices.
"""

import os
import json
import argparse
from datetime import datetime
from pathlib import Path

# Template per plugin.json
PLUGIN_JSON_TEMPLATE = {
    "name": "",
    "description": "",
    "author": "",
    "version": "1.0.0",
    "tags": [],
    "thumb": "",
    "url": ""
}

# Template per plugin base
BASIC_PLUGIN_TEMPLATE = '''"""
{description}

Plugin creato con Stregatto Plugin Creator
Autore: {author}
Data: {date}
"""

from cat.mad_hatter.decorators import tool

@tool
def {tool_name}(input, cat):
    """
    {tool_description}
    
    Args:
        input: Input dall'utente
        cat: Istanza StrayCat
    
    Returns:
        str: Risposta per l'utente
    """
    
    try:
        # Il tuo codice qui
        result = f"Ciao! Hai scritto: {{input}}"
        
        return result
        
    except Exception as e:
        return f"Errore: {{str(e)}}"
'''

# Template per plugin con hook
HOOK_PLUGIN_TEMPLATE = '''"""
{description}

Plugin con hook per personalizzare il comportamento del Cat.
"""

from cat.mad_hatter.decorators import hook

@hook
def before_cat_sends_message(message, cat):
    """
    Personalizza i messaggi prima dell'invio.
    
    Args:
        message: Messaggio da inviare
        cat: Istanza StrayCat
    
    Returns:
        dict: Messaggio modificato
    """
    
    # Esempio: aggiungi emoji
    message["content"] += " 😸"
    
    return message

@hook(priority=10)
def agent_prompt_prefix(prefix, cat):
    """
    Personalizza la personalità del Cat.
    
    Args:
        prefix: Prompt prefix attuale
        cat: Istanza StrayCat
    
    Returns:
        str: Nuovo prompt prefix
    """
    
    prefix = """Sei un assistente AI specializzato e amichevole.
    Rispondi sempre in modo professionale ma cordiale.
    """
    
    return prefix
'''

# Template per plugin con form
FORM_PLUGIN_TEMPLATE = '''"""
{description}

Plugin con form per conversazioni strutturate.
"""

from cat.mad_hatter.decorators import form
from cat.experimental.form import CatForm
from pydantic import BaseModel, Field

class {form_class_name}Data(BaseModel):
    nome: str = Field(description="Il tuo nome")
    email: str = Field(regex=r'^[^@]+@[^@]+\\.[^@]+$', description="La tua email")
    messaggio: str = Field(min_length=10, description="Il tuo messaggio")

@form
class {form_class_name}(CatForm):
    description = "{form_description}"
    model_class = {form_class_name}Data
    start_examples = [
        "compila form",
        "voglio inviare un messaggio",
        "contattaci"
    ]
    stop_examples = [
        "annulla",
        "stop"
    ]
    ask_confirm = True
    
    def submit(self, form_data):
        """
        Chiamato quando il form è completato.
        
        Args:
            form_data: Dati raccolti dal form
        
        Returns:
            dict: Risposta di conferma
        """
        
        # Processa i dati del form
        nome = form_data['nome']
        email = form_data['email']
        messaggio = form_data['messaggio']
        
        # Il tuo codice qui
        # Ad esempio: salva nel database, invia email, ecc.
        
        return {{
            "output": f"Grazie {{nome}}! Il tuo messaggio è stato ricevuto."
        }}
'''

def create_plugin_directory(name, base_path="plugins"):
    """Crea la directory del plugin."""
    plugin_path = Path(base_path) / name
    plugin_path.mkdir(parents=True, exist_ok=True)
    return plugin_path

def create_plugin_json(plugin_path, name, description, author, tags):
    """Crea il file plugin.json."""
    plugin_json = PLUGIN_JSON_TEMPLATE.copy()
    plugin_json["name"] = name
    plugin_json["description"] = description
    plugin_json["author"] = author
    plugin_json["tags"] = tags
    
    json_path = plugin_path / "plugin.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(plugin_json, f, indent=2, ensure_ascii=False)
    
    return json_path

def create_main_file(plugin_path, template_type, **kwargs):
    """Crea il file principale del plugin."""
    
    templates = {
        'basic': BASIC_PLUGIN_TEMPLATE,
        'hook': HOOK_PLUGIN_TEMPLATE,
        'form': FORM_PLUGIN_TEMPLATE
    }
    
    template = templates.get(template_type, BASIC_PLUGIN_TEMPLATE)
    
    # Sostituisci i placeholder
    content = template.format(**kwargs)
    
    main_path = plugin_path / f"{kwargs.get('plugin_name', 'main')}.py"
    with open(main_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return main_path

def create_readme(plugin_path, name, description, author):
    """Crea il file README.md."""
    
    readme_content = f"""# {name}

{description}

## 🎯 Funzionalità

- Descrivi qui le funzionalità principali
- Aggiungi esempi di utilizzo
- Spiega come configurare il plugin

## 🚀 Installazione

1. Copia questa cartella in `cat/plugins/`
2. Riavvia Cheshire Cat
3. Il plugin sarà automaticamente attivo

## 🧪 Come Usare

Descrivi qui come usare il plugin:

- Comando 1: "esempio comando"
- Comando 2: "altro esempio"

## ⚙️ Configurazione

Se il plugin ha configurazioni, spiegale qui.

## 🐛 Risoluzione Problemi

Problemi comuni e soluzioni.

## 📝 Changelog

### v1.0.0
- Prima versione
- Funzionalità base implementate

## 👤 Autore

**{author}**

## 📄 Licenza

MIT License
"""
    
    readme_path = plugin_path / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    return readme_path

def create_requirements(plugin_path, requirements=None):
    """Crea il file requirements.txt se necessario."""
    if not requirements:
        return None
    
    req_path = plugin_path / "requirements.txt"
    with open(req_path, 'w', encoding='utf-8') as f:
        for req in requirements:
            f.write(f"{req}\n")
    
    return req_path

def main():
    parser = argparse.ArgumentParser(
        description="🔨 Crea un nuovo plugin per Cheshire Cat AI"
    )
    
    parser.add_argument(
        "name",
        help="Nome del plugin (es: mio_plugin)"
    )
    
    parser.add_argument(
        "--description", "-d",
        default="Un nuovo plugin per Cheshire Cat AI",
        help="Descrizione del plugin"
    )
    
    parser.add_argument(
        "--author", "-a",
        default="Il Tuo Nome",
        help="Nome dell'autore"
    )
    
    parser.add_argument(
        "--type", "-t",
        choices=['basic', 'hook', 'form'],
        default='basic',
        help="Tipo di plugin da creare"
    )
    
    parser.add_argument(
        "--tags",
        nargs='*',
        default=['utility'],
        help="Tag per il plugin"
    )
    
    parser.add_argument(
        "--requirements", "-r",
        nargs='*',
        help="Dipendenze Python da aggiungere"
    )
    
    parser.add_argument(
        "--output", "-o",
        default="plugins",
        help="Directory di output"
    )
    
    args = parser.parse_args()
    
    print(f"🔨 Creando plugin '{args.name}'...")
    print(f"📝 Tipo: {args.type}")
    print(f"👤 Autore: {args.author}")
    print(f"🏷️ Tag: {', '.join(args.tags)}")
    print()
    
    # Crea directory plugin
    plugin_path = create_plugin_directory(args.name, args.output)
    print(f"📁 Directory creata: {plugin_path}")
    
    # Crea plugin.json
    json_path = create_plugin_json(
        plugin_path, args.name, args.description, args.author, args.tags
    )
    print(f"📄 Creato: {json_path}")
    
    # Prepara parametri per il template
    template_params = {
        'plugin_name': args.name,
        'description': args.description,
        'author': args.author,
        'date': datetime.now().strftime('%Y-%m-%d'),
        'tool_name': f"{args.name.replace('-', '_')}_tool",
        'tool_description': f"Tool principale per {args.name}",
        'form_class_name': ''.join(word.capitalize() for word in args.name.split('_')),
        'form_description': f"Form per {args.name}"
    }
    
    # Crea file principale
    main_path = create_main_file(plugin_path, args.type, **template_params)
    print(f"🐍 Creato: {main_path}")
    
    # Crea README
    readme_path = create_readme(plugin_path, args.name, args.description, args.author)
    print(f"📖 Creato: {readme_path}")
    
    # Crea requirements.txt se necessario
    if args.requirements:
        req_path = create_requirements(plugin_path, args.requirements)
        print(f"📦 Creato: {req_path}")
    
    print()
    print("✅ Plugin creato con successo!")
    print()
    print("🎯 Prossimi passi:")
    print(f"1. Modifica il codice in {main_path}")
    print(f"2. Aggiorna la documentazione in {readme_path}")
    print(f"3. Copia la cartella in cat/plugins/")
    print("4. Riavvia Cheshire Cat")
    print()
    print("💡 Suggerimenti:")
    print("- Studia gli esempi in examples/")
    print("- Leggi la documentazione in docs/")
    print("- Testa il plugin prima di condividerlo")

if __name__ == "__main__":
    main()
