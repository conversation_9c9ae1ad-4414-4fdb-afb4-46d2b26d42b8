#!/bin/bash

# 🐱 Script di Setup per Stregatto - Guida Cheshire Cat AI
# Questo script ti aiuta a configurare tutto per iniziare

echo "🐱 Benvenuto in Stregatto - Guida Cheshire Cat AI!"
echo "=================================================="
echo ""

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funzione per stampare messaggi colorati
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Controlla se Docker è installato
check_docker() {
    if command -v docker &> /dev/null; then
        print_status "Docker è installato"
        return 0
    else
        print_error "Docker non è installato"
        return 1
    fi
}

# Controlla se Docker Compose è installato
check_docker_compose() {
    if command -v docker-compose &> /dev/null || docker compose version &> /dev/null; then
        print_status "Docker Compose è installato"
        return 0
    else
        print_error "Docker Compose non è installato"
        return 1
    fi
}

# Controlla se Python è installato
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_status "Python $PYTHON_VERSION è installato"
        return 0
    else
        print_error "Python 3 non è installato"
        return 1
    fi
}

# Controlla se Git è installato
check_git() {
    if command -v git &> /dev/null; then
        print_status "Git è installato"
        return 0
    else
        print_error "Git non è installato"
        return 1
    fi
}

# Menu principale
show_menu() {
    echo ""
    echo "🎯 Cosa vuoi fare?"
    echo "=================="
    echo "1) 🔍 Controlla prerequisiti"
    echo "2) 📥 Installa Cheshire Cat con Docker"
    echo "3) 🐍 Installa Cheshire Cat manualmente"
    echo "4) 🔌 Copia plugin di esempio"
    echo "5) 📚 Apri documentazione"
    echo "6) 🆘 Aiuto e supporto"
    echo "0) ❌ Esci"
    echo ""
    read -p "Scegli un'opzione (0-6): " choice
}

# Controlla tutti i prerequisiti
check_prerequisites() {
    echo ""
    echo "🔍 Controllo prerequisiti..."
    echo "============================"
    
    check_docker
    DOCKER_OK=$?
    
    check_docker_compose
    COMPOSE_OK=$?
    
    check_python
    PYTHON_OK=$?
    
    check_git
    GIT_OK=$?
    
    echo ""
    if [ $DOCKER_OK -eq 0 ] && [ $COMPOSE_OK -eq 0 ]; then
        print_status "✅ Tutti i prerequisiti per Docker sono soddisfatti!"
        print_info "Puoi procedere con l'installazione Docker (opzione 2)"
    elif [ $PYTHON_OK -eq 0 ] && [ $GIT_OK -eq 0 ]; then
        print_status "✅ Prerequisiti per installazione manuale soddisfatti!"
        print_info "Puoi procedere con l'installazione manuale (opzione 3)"
    else
        print_warning "⚠️ Alcuni prerequisiti mancano. Installa i componenti mancanti."
    fi
}

# Installa con Docker
install_docker() {
    echo ""
    echo "📥 Installazione Cheshire Cat con Docker..."
    echo "============================================"
    
    if ! check_docker || ! check_docker_compose; then
        print_error "Docker o Docker Compose non disponibili!"
        return 1
    fi
    
    print_info "Clonando il repository Cheshire Cat..."
    
    if [ -d "cheshire-cat-ai" ]; then
        print_warning "Directory cheshire-cat-ai già esistente. Aggiorno..."
        cd cheshire-cat-ai
        git pull
    else
        git clone https://github.com/cheshire-cat-ai/core.git cheshire-cat-ai
        cd cheshire-cat-ai
    fi
    
    print_info "Avviando Cheshire Cat con Docker..."
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        print_status "🎉 Cheshire Cat avviato con successo!"
        print_info "Apri il browser su: http://localhost:1865/admin"
        print_info "Per vedere i log: docker-compose logs -f"
        print_info "Per fermare: docker-compose down"
    else
        print_error "Errore nell'avvio di Cheshire Cat"
    fi
    
    cd ..
}

# Installa manualmente
install_manual() {
    echo ""
    echo "🐍 Installazione manuale Cheshire Cat..."
    echo "========================================"
    
    if ! check_python || ! check_git; then
        print_error "Python 3 o Git non disponibili!"
        return 1
    fi
    
    print_info "Clonando il repository..."
    
    if [ -d "cheshire-cat-ai" ]; then
        print_warning "Directory cheshire-cat-ai già esistente. Aggiorno..."
        cd cheshire-cat-ai
        git pull
    else
        git clone https://github.com/cheshire-cat-ai/core.git cheshire-cat-ai
        cd cheshire-cat-ai
    fi
    
    print_info "Creando ambiente virtuale..."
    python3 -m venv venv
    
    print_info "Attivando ambiente virtuale..."
    source venv/bin/activate
    
    print_info "Installando dipendenze..."
    pip install -r requirements.txt
    
    print_info "Avviando Cheshire Cat..."
    python main.py &
    
    print_status "🎉 Cheshire Cat avviato!"
    print_info "Apri il browser su: http://localhost:1865/admin"
    print_info "Per fermare: usa Ctrl+C o kill il processo"
    
    cd ..
}

# Copia plugin di esempio
copy_examples() {
    echo ""
    echo "🔌 Copia plugin di esempio..."
    echo "============================="
    
    if [ ! -d "cheshire-cat-ai" ]; then
        print_error "Directory cheshire-cat-ai non trovata!"
        print_info "Installa prima Cheshire Cat (opzione 2 o 3)"
        return 1
    fi
    
    PLUGIN_DIR="cheshire-cat-ai/cat/plugins"
    
    if [ ! -d "$PLUGIN_DIR" ]; then
        print_error "Directory plugin non trovata in $PLUGIN_DIR"
        return 1
    fi
    
    print_info "Copiando plugin di esempio..."
    
    # Copia tutti gli esempi
    for example in examples/*/; do
        if [ -d "$example" ]; then
            example_name=$(basename "$example")
            print_info "Copiando $example_name..."
            cp -r "$example" "$PLUGIN_DIR/"
        fi
    done
    
    print_status "✅ Plugin di esempio copiati!"
    print_info "Riavvia Cheshire Cat per caricare i nuovi plugin"
}

# Apri documentazione
open_docs() {
    echo ""
    echo "📚 Aprendo documentazione..."
    echo "============================"
    
    # Prova ad aprire il browser
    if command -v xdg-open &> /dev/null; then
        xdg-open "docs/01-introduzione.md"
    elif command -v open &> /dev/null; then
        open "docs/01-introduzione.md"
    else
        print_info "Apri manualmente il file: docs/01-introduzione.md"
    fi
    
    print_info "📖 Documentazione disponibile in:"
    echo "   • docs/01-introduzione.md"
    echo "   • docs/02-installazione.md"
    echo "   • docs/03-concetti-base.md"
    echo "   • docs/04-plugin-tools.md"
    echo "   • docs/05-plugin-hooks.md"
    echo "   • docs/06-plugin-forms.md"
    echo "   • docs/07-esempi-avanzati.md"
}

# Aiuto e supporto
show_help() {
    echo ""
    echo "🆘 Aiuto e Supporto"
    echo "=================="
    echo ""
    echo "📚 Risorse utili:"
    echo "• Documentazione ufficiale: https://cheshire-cat-ai.github.io/docs/"
    echo "• Repository GitHub: https://github.com/cheshire-cat-ai/core"
    echo "• Discord Community: https://discord.gg/bHX5sNFCYU"
    echo "• Plugin Registry: https://github.com/cheshire-cat-ai/plugins"
    echo ""
    echo "🐛 Problemi comuni:"
    echo "• Porta 1865 occupata: sudo lsof -i :1865"
    echo "• Problemi Docker: docker-compose logs"
    echo "• Problemi Python: controlla versione Python 3.10+"
    echo ""
    echo "💡 Suggerimenti:"
    echo "• Inizia con docs/01-introduzione.md"
    echo "• Prova gli esempi in examples/"
    echo "• Unisciti alla community Discord per aiuto"
}

# Loop principale
main() {
    while true; do
        show_menu
        
        case $choice in
            1)
                check_prerequisites
                ;;
            2)
                install_docker
                ;;
            3)
                install_manual
                ;;
            4)
                copy_examples
                ;;
            5)
                open_docs
                ;;
            6)
                show_help
                ;;
            0)
                echo ""
                print_status "👋 Grazie per aver usato Stregatto!"
                print_info "Buon divertimento con Cheshire Cat AI! 🐱"
                exit 0
                ;;
            *)
                print_error "Opzione non valida. Scegli un numero da 0 a 6."
                ;;
        esac
        
        echo ""
        read -p "Premi INVIO per continuare..."
    done
}

# Avvia il programma
main
