# 🧠 Concetti Base di Cheshire Cat AI

Ora che hai installato il Cat, è tempo di capire come funziona internamente. Questa guida ti spiegherà i concetti fondamentali con esempi pratici.

## 🏗️ Architettura del Sistema

### Il Flusso di una Conversazione

```mermaid
sequenceDiagram
    participant U as Utente
    participant C as Cheshire Cat
    participant M as Memoria
    participant LL<PERSON> as Language Model
    participant P as Plugin

    U->>C: "Che ore sono?"
    C->>M: Cerca conversazioni passate
    C->>M: Cerca documenti rilevanti
    C->>LLM: Genera prompt con contesto
    LLM->>C: Risposta + possibili tool calls
    C->>P: Esegue tool "get_time"
    P->>C: "14:30"
    C->>LLM: Genera risposta finale
    LLM->>C: "Sono le 14:30"
    C->>M: Salva conversazione
    C->>U: "Sono le 14:30"
```

## 🧠 Sistema di Memoria

Il Cat ha tre tipi di memoria, come un cervello umano:

### 1. 📖 Memoria Episodica
**Cosa ricorda**: Tutte le conversazioni passate

```python
# Esempio di memoria episodica
{
    "timestamp": "2024-01-15T14:30:00Z",
    "user_id": "user123",
    "human_message": "Come ti chiami?",
    "ai_message": "Sono il Cheshire Cat!",
    "metadata": {"session_id": "abc123"}
}
```

**Quando viene usata**:
- "Ti ricordi cosa mi hai detto ieri?"
- "Continuiamo il discorso di prima"
- Mantenere il contesto della conversazione

### 2. 📚 Memoria Dichiarativa
**Cosa ricorda**: Documenti e conoscenze caricate

```python
# Esempio di documento in memoria
{
    "content": "Il gatto è un felino domestico...",
    "source": "enciclopedia_animali.pdf",
    "page": 42,
    "embedding": [0.1, 0.2, 0.3, ...],  # Vettore per ricerca semantica
    "metadata": {"category": "animali", "date": "2024-01-15"}
}
```

**Quando viene usata**:
- "Cosa dice il manuale sulla procedura X?"
- "Trova informazioni sui gatti nel documento"
- Rispondere con conoscenze specifiche

### 3. 🔧 Memoria Procedurale
**Cosa ricorda**: Tools e azioni disponibili

```python
# Esempio di tool in memoria
{
    "name": "get_weather",
    "description": "Ottiene il meteo per una città",
    "parameters": {"city": "string"},
    "embedding": [0.4, 0.5, 0.6, ...],
    "source_plugin": "weather_plugin"
}
```

**Quando viene usata**:
- "Che tempo fa a Roma?" → usa `get_weather`
- "Calcola 2+2" → usa `calculator`
- Eseguire azioni specifiche

## 🔍 Retrieval Augmented Generation (RAG)

Il Cat usa RAG per combinare la potenza degli LLM con le tue conoscenze specifiche:

### Come Funziona RAG

1. **📥 Ingestion**: I documenti vengono divisi in chunks e convertiti in vettori
2. **🔍 Retrieval**: Per ogni domanda, trova i chunks più rilevanti
3. **🧠 Generation**: L'LLM genera la risposta usando i chunks come contesto

```python
# Esempio di RAG in azione
user_question = "Come si fa il pane?"

# 1. Cerca chunks rilevanti
relevant_chunks = [
    "Per fare il pane servono: farina, acqua, lievito, sale...",
    "Impastare per 10 minuti fino a ottenere un composto liscio...",
    "Lasciar lievitare per 2 ore in luogo tiepido..."
]

# 2. Crea prompt con contesto
prompt = f"""
Contesto: {relevant_chunks}
Domanda: {user_question}
Rispondi usando le informazioni del contesto.
"""

# 3. LLM genera risposta
response = llm.generate(prompt)
```

## 🔌 Sistema di Plugin

I plugin sono il cuore dell'estensibilità del Cat:

### Struttura di un Plugin

```
mio_plugin/
├── plugin.json          # Metadati del plugin
├── main.py             # Codice principale
├── requirements.txt    # Dipendenze Python
└── README.md          # Documentazione
```

### plugin.json
```json
{
    "name": "Il Mio Plugin",
    "description": "Fa cose fantastiche",
    "author": "Il Tuo Nome",
    "version": "1.0.0",
    "tags": ["utility", "esempio"]
}
```

### Tipi di Componenti Plugin

#### 🧰 Tools
Funzioni che il Cat può eseguire:

```python
from cat.mad_hatter.decorators import tool

@tool
def calcola_area_cerchio(raggio, cat):
    """Calcola l'area di un cerchio dato il raggio."""
    import math
    area = math.pi * raggio ** 2
    return f"L'area del cerchio è {area:.2f}"
```

#### 🪝 Hooks
Punti di personalizzazione del comportamento:

```python
from cat.mad_hatter.decorators import hook

@hook
def before_cat_sends_message(message, cat):
    """Modifica il messaggio prima dell'invio."""
    # Aggiungi emoji alla fine
    message["content"] += " 😸"
    return message
```

#### 📋 Forms
Conversazioni strutturate:

```python
from cat.experimental.form import form, CatForm
from pydantic import BaseModel

class OrderData(BaseModel):
    product: str
    quantity: int
    email: str

@form
class OrderForm(CatForm):
    description = "Ordine Prodotto"
    model_class = OrderData
    start_examples = ["voglio ordinare", "fai un ordine"]
    
    def submit(self, form_data):
        return f"Ordine ricevuto: {form_data}"
```

## 🎭 I Personaggi del Framework

### 😺 StrayCat
L'istanza del Cat per ogni utente:

```python
# Cosa puoi fare con StrayCat
def my_tool(input, cat):
    # Accedi alla memoria
    memories = cat.recall_relevant_memories("gatti")
    
    # Usa l'LLM direttamente
    response = cat.llm("Scrivi una poesia sui gatti")
    
    # Accedi alla working memory
    cat.working_memory["last_action"] = "poetry"
    
    # Invia messaggi
    cat.send_ws_message({"type": "notification", "text": "Fatto!"})
```

### 🎩 Mad Hatter
Gestisce i plugin:

```python
# Accedi ai plugin installati
active_plugins = cat.mad_hatter.plugins
plugin_info = cat.mad_hatter.get_plugin("mio_plugin")

# Installa/disinstalla plugin
cat.mad_hatter.install_plugin("path/to/plugin")
cat.mad_hatter.toggle_plugin("mio_plugin")
```

### 🐰 White Rabbit
Gestisce azioni programmate:

```python
# Programma un'azione
cat.white_rabbit.schedule_job(
    job_id="daily_report",
    job=lambda: send_daily_report(),
    trigger="cron",
    hour=9,
    minute=0
)
```

### 🕳️ Rabbit Hole
Processa documenti:

```python
# Carica un documento
cat.rabbit_hole.ingest_file("path/to/document.pdf")

# Carica testo direttamente
cat.rabbit_hole.ingest_text("Questo è un testo importante", source="manual")
```

## 🔄 Ciclo di Vita di una Richiesta

### 1. **Ricezione** 📥
```python
# L'utente invia un messaggio
user_message = "Che tempo fa oggi?"
```

### 2. **Preprocessing** 🔄
```python
# Hook: before_cat_reads_message
message = apply_hooks("before_cat_reads_message", message)
```

### 3. **Memory Recall** 🧠
```python
# Cerca in tutte le memorie
episodic_memories = cat.recall_episodic_memories(user_message)
declarative_memories = cat.recall_declarative_memories(user_message)
procedural_memories = cat.recall_procedural_memories(user_message)
```

### 4. **Agent Reasoning** 🤔
```python
# L'agent decide cosa fare
agent_output = cat.main_agent.execute({
    "input": user_message,
    "episodic_memory": episodic_memories,
    "declarative_memory": declarative_memories,
    "procedural_memory": procedural_memories
})
```

### 5. **Tool Execution** ⚡
```python
# Se necessario, esegue tools
if agent_output.tool_calls:
    for tool_call in agent_output.tool_calls:
        result = execute_tool(tool_call)
        agent_output.intermediate_steps.append(result)
```

### 6. **Response Generation** 💭
```python
# Genera la risposta finale
final_response = cat.main_agent.generate_final_response(agent_output)
```

### 7. **Postprocessing** 📤
```python
# Hook: before_cat_sends_message
final_response = apply_hooks("before_cat_sends_message", final_response)
```

### 8. **Memory Storage** 💾
```python
# Salva la conversazione
cat.memory.working_memory.update({
    "user_message": user_message,
    "ai_message": final_response,
    "timestamp": datetime.now()
})
```

## 🎯 Best Practices

### 1. **Memoria Efficiente**
- Usa metadati descrittivi nei documenti
- Dividi documenti grandi in sezioni logiche
- Pulisci periodicamente la memoria episodica

### 2. **Plugin Performanti**
- Mantieni i tools semplici e veloci
- Usa caching per operazioni costose
- Gestisci gli errori gracefully

### 3. **Sicurezza**
- Valida sempre gli input degli utenti
- Non esporre informazioni sensibili
- Usa autenticazione per API critiche

---

**Pronto per i plugin?** Vai al prossimo capitolo: [04-plugin-tools.md](04-plugin-tools.md) 🚀
