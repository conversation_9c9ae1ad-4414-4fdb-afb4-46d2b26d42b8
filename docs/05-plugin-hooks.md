# 🪝 Plugin Hooks - Personalizzare il Comportamento del Cat

Gli **Hooks** sono punti di aggancio nel flusso di esecuzione del Cheshire Cat che ti permettono di personalizzare completamente il suo comportamento senza modificare il codice core.

## 🎯 Cosa Sono gli Hooks?

Un **Hook** è una funzione Python che viene chiamata automaticamente dal framework in momenti specifici:

- **Prima** che il Cat legga un messaggio
- **Durante** la generazione della risposta  
- **Dopo** che il Cat invia un messaggio
- **Quando** vengono caricati documenti
- E molti altri momenti...

### Esempio Semplice

```python
from cat.mad_hatter.decorators import hook

@hook
def before_cat_sends_message(message, cat):
    """Aggiunge un emoji alla fine di ogni messaggio."""
    message["content"] += " 😸"
    return message
```

## 🏗️ Anatomia di un Hook

### Struttura Base

```python
from cat.mad_hatter.decorators import hook

@hook(priority=1)  # Priorità opzionale (default: 1)
def nome_hook(parametri, cat):
    """Descrizione di cosa fa l'hook.
    
    Args:
        parametri: Dati specifici dell'hook
        cat: Istanza StrayCat
    
    Returns:
        Dati modificati (stesso tipo dell'input)
    """
    # Modifica i parametri
    # ...
    return parametri
```

### Sistema di Priorità

Gli hooks con **priorità più alta** vengono eseguiti **prima**:

```python
@hook(priority=10)  # Eseguito per primo
def hook_alta_priorita(data, cat):
    data["step"] = "primo"
    return data

@hook(priority=5)   # Eseguito secondo
def hook_media_priorita(data, cat):
    data["step"] = "secondo"
    return data

@hook(priority=1)   # Eseguito ultimo (default)
def hook_bassa_priorita(data, cat):
    data["step"] = "ultimo"
    return data
```

## 📋 Hooks Disponibili

### 1. 💬 Hooks di Conversazione

#### `before_cat_reads_message`
Modifica il messaggio dell'utente prima che venga processato:

```python
@hook
def before_cat_reads_message(user_message_json, cat):
    """Preprocessa i messaggi degli utenti."""
    
    # Correggi errori di battitura comuni
    text = user_message_json["text"]
    text = text.replace("perchè", "perché")
    text = text.replace("xchè", "perché")
    
    user_message_json["text"] = text
    return user_message_json
```

#### `before_cat_sends_message`
Modifica la risposta prima che venga inviata:

```python
@hook
def before_cat_sends_message(message, cat):
    """Personalizza le risposte del Cat."""
    
    # Aggiungi timestamp
    from datetime import datetime
    timestamp = datetime.now().strftime("%H:%M")
    message["content"] = f"[{timestamp}] {message['content']}"
    
    # Aggiungi metadati
    message["metadata"] = {
        "processed_by": "mio_plugin",
        "user_id": cat.user_id
    }
    
    return message
```

### 2. 🧠 Hooks di Memoria

#### `before_cat_stores_episodic_memory`
Modifica come vengono salvate le conversazioni:

```python
@hook
def before_cat_stores_episodic_memory(doc, cat):
    """Arricchisce la memoria episodica."""
    
    # Aggiungi sentiment analysis
    sentiment = analyze_sentiment(doc.page_content)
    doc.metadata["sentiment"] = sentiment
    
    # Aggiungi categoria automatica
    if "problema" in doc.page_content.lower():
        doc.metadata["category"] = "support"
    elif "ordine" in doc.page_content.lower():
        doc.metadata["category"] = "sales"
    
    return doc
```

#### `before_cat_recalls_memories`
Personalizza come vengono recuperate le memorie:

```python
@hook
def before_cat_recalls_memories(query, cat):
    """Migliora le query di ricerca memoria."""
    
    # Espandi sinonimi
    synonyms = {
        "gatto": ["felino", "micio", "cat"],
        "cane": ["canino", "dog", "cagnolino"]
    }
    
    for word, syns in synonyms.items():
        if word in query.lower():
            query += " " + " ".join(syns)
    
    return query
```

### 3. 🎭 Hooks di Personalità

#### `agent_prompt_prefix`
Cambia la personalità base del Cat:

```python
@hook(priority=10)  # Alta priorità per sovrascrivere il default
def agent_prompt_prefix(prefix, cat):
    """Trasforma il Cat in un assistente specializzato."""
    
    prefix = """Sei un esperto consulente finanziario AI.
    Sei professionale, preciso e sempre aggiornato sulle normative.
    Fornisci consigli pratici e ben documentati.
    Non dare mai consigli di investimento specifici senza disclaimer."""
    
    return prefix
```

#### `agent_prompt_suffix`
Aggiunge istruzioni finali al prompt:

```python
@hook
def agent_prompt_suffix(suffix, cat):
    """Aggiunge istruzioni specifiche."""
    
    suffix += """
    
    IMPORTANTE:
    - Rispondi sempre in italiano
    - Usa un tono amichevole ma professionale
    - Se non sei sicuro, dillo chiaramente
    - Cita sempre le fonti quando possibile
    """
    
    return suffix
```

### 4. 📚 Hooks di Documenti

#### `before_rabbithole_stores_documents`
Preprocessa i documenti prima del salvataggio:

```python
@hook
def before_rabbithole_stores_documents(doc, cat):
    """Arricchisce i documenti con metadati."""
    
    # Rileva la lingua
    from langdetect import detect
    try:
        doc.metadata["language"] = detect(doc.page_content)
    except:
        doc.metadata["language"] = "unknown"
    
    # Estrai entità
    entities = extract_entities(doc.page_content)
    doc.metadata["entities"] = entities
    
    # Calcola readability score
    doc.metadata["readability"] = calculate_readability(doc.page_content)
    
    return doc
```

#### `before_rabbithole_splits_text`
Personalizza come vengono divisi i documenti:

```python
@hook
def before_rabbithole_splits_text(text, cat):
    """Preprocessa il testo prima della divisione."""
    
    # Pulisci il testo
    import re
    
    # Rimuovi caratteri speciali
    text = re.sub(r'[^\w\s\.\,\!\?\;\:]', ' ', text)
    
    # Normalizza spazi
    text = re.sub(r'\s+', ' ', text)
    
    # Rimuovi righe troppo corte (probabilmente errori OCR)
    lines = text.split('\n')
    lines = [line for line in lines if len(line.strip()) > 10]
    text = '\n'.join(lines)
    
    return text
```

## 🎨 Esempi Avanzati

### 1. 🔐 Sistema di Autenticazione

```python
@hook(priority=100)  # Massima priorità
def before_cat_reads_message(user_message_json, cat):
    """Implementa autenticazione semplice."""
    
    # Controlla se l'utente è autenticato
    if not hasattr(cat, 'authenticated') or not cat.authenticated:
        
        text = user_message_json["text"].strip()
        
        # Comando di login
        if text.startswith("/login "):
            password = text[7:]  # Rimuovi "/login "
            
            if password == "password123":  # Password semplice (usa hash in produzione!)
                cat.authenticated = True
                user_message_json["text"] = "Login effettuato con successo!"
            else:
                user_message_json["text"] = "Password errata. Riprova con /login <password>"
        else:
            # Blocca tutti gli altri messaggi
            user_message_json["text"] = "Devi autenticarti prima. Usa: /login <password>"
    
    return user_message_json
```

### 2. 📊 Sistema di Analytics

```python
import json
from datetime import datetime

@hook
def before_cat_sends_message(message, cat):
    """Traccia analytics delle conversazioni."""
    
    # File di log analytics
    analytics_file = "cat/data/analytics.jsonl"
    
    # Raccogli metriche
    analytics_data = {
        "timestamp": datetime.now().isoformat(),
        "user_id": getattr(cat, 'user_id', 'anonymous'),
        "message_length": len(message["content"]),
        "response_time": getattr(cat, 'response_time', 0),
        "tools_used": getattr(cat, 'tools_used_in_session', []),
        "memory_recalls": getattr(cat, 'memory_recalls_count', 0)
    }
    
    # Salva nel file
    try:
        with open(analytics_file, 'a') as f:
            f.write(json.dumps(analytics_data) + '\n')
    except Exception as e:
        cat.log.error(f"Errore nel salvare analytics: {e}")
    
    return message
```

### 3. 🎯 Sistema di Moderazione

```python
@hook(priority=50)
def before_cat_reads_message(user_message_json, cat):
    """Modera i contenuti inappropriati."""
    
    text = user_message_json["text"].lower()
    
    # Lista di parole vietate (esempio semplificato)
    banned_words = ["spam", "offensivo", "inappropriato"]
    
    # Controlla contenuti
    for word in banned_words:
        if word in text:
            user_message_json["text"] = "Mi dispiace, non posso rispondere a questo tipo di contenuto."
            
            # Log dell'incidente
            cat.log.warning(f"Contenuto moderato per utente {getattr(cat, 'user_id', 'unknown')}")
            break
    
    return user_message_json

@hook
def before_cat_sends_message(message, cat):
    """Modera anche le risposte del Cat."""
    
    content = message["content"].lower()
    
    # Controlla che il Cat non generi contenuti inappropriati
    sensitive_topics = ["violenza", "contenuto esplicito"]
    
    for topic in sensitive_topics:
        if topic in content:
            message["content"] = "Mi dispiace, non posso fornire informazioni su questo argomento."
            break
    
    return message
```

### 4. 🌍 Sistema Multi-lingua

```python
@hook
def before_cat_reads_message(user_message_json, cat):
    """Rileva e gestisce lingue diverse."""
    
    from langdetect import detect
    
    try:
        # Rileva lingua del messaggio
        detected_lang = detect(user_message_json["text"])
        
        # Salva nella working memory
        cat.working_memory["user_language"] = detected_lang
        
        # Se non è italiano, traduci
        if detected_lang != 'it':
            translated = translate_to_italian(user_message_json["text"])
            user_message_json["original_text"] = user_message_json["text"]
            user_message_json["text"] = translated
            user_message_json["translated"] = True
            
    except Exception as e:
        cat.log.error(f"Errore nella rilevazione lingua: {e}")
    
    return user_message_json

@hook
def before_cat_sends_message(message, cat):
    """Traduci la risposta nella lingua dell'utente."""
    
    user_lang = cat.working_memory.get("user_language", "it")
    
    # Se l'utente non parla italiano, traduci la risposta
    if user_lang != 'it':
        try:
            translated_response = translate_from_italian(message["content"], user_lang)
            message["content"] = translated_response
            message["translated_to"] = user_lang
        except Exception as e:
            cat.log.error(f"Errore nella traduzione: {e}")
    
    return message
```

## 🔧 Best Practices per Hooks

### 1. **Gestione Errori**
```python
@hook
def safe_hook(data, cat):
    """Hook con gestione errori robusta."""
    try:
        # Logica dell'hook
        modified_data = process_data(data)
        return modified_data
    except Exception as e:
        # Log dell'errore
        cat.log.error(f"Errore in safe_hook: {e}")
        # Restituisci i dati originali
        return data
```

### 2. **Performance**
```python
@hook
def efficient_hook(data, cat):
    """Hook ottimizzato per performance."""
    # Evita operazioni costose se non necessarie
    if not should_process(data):
        return data
    
    # Usa caching quando possibile
    cache_key = f"hook_cache_{hash(str(data))}"
    if cache_key in cat.working_memory:
        return cat.working_memory[cache_key]
    
    # Processa
    result = expensive_operation(data)
    cat.working_memory[cache_key] = result
    
    return result
```

### 3. **Compatibilità**
```python
@hook
def compatible_hook(data, cat):
    """Hook compatibile con diverse versioni."""
    # Controlla se il campo esiste
    if hasattr(data, 'new_field'):
        data.new_field = "modified"
    
    # Gestisci diversi tipi di input
    if isinstance(data, dict):
        data["processed"] = True
    elif hasattr(data, 'metadata'):
        data.metadata["processed"] = True
    
    return data
```

## 🎯 Prossimi Passi

Ora che padroneggi gli Hooks:

1. **🧪 Sperimenta**: Prova gli esempi di questa guida
2. **🔗 Combina**: Usa Hooks e Tools insieme
3. **📋 Impara i Forms**: [06-plugin-forms.md](06-plugin-forms.md)

---

**Pronto per conversazioni strutturate?** Vai al prossimo capitolo: [06-plugin-forms.md](06-plugin-forms.md) 🚀
