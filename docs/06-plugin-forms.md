# 📋 Plugin Forms - Conversazioni Strutturate

I **Forms** sono il modo per creare conversazioni strutturate multi-turno con il tuo Cheshire Cat. Pensa ai Forms come ai moduli HTML, ma per le conversazioni!

## 🎯 Cosa Sono i Forms?

Un **Form** è una conversazione guidata che:
- **Raccoglie informazioni** specifiche dall'utente
- **Valida i dati** inseriti
- **Gestisce errori** e richieste di chiarimento
- **Esegue azioni** quando tutti i dati sono raccolti

### Differenza tra Tools e Forms

| **Tools** | **Forms** |
|-----------|-----------|
| ⚡ One-shot (una chiamata) | 🔄 Multi-turno (più messaggi) |
| 🎯 Azione immediata | 📝 Raccolta dati strutturata |
| 🤖 Scelti dal LLM | 👤 Attivati da trigger specifici |

## 🏗️ Anatomia di un Form

### Struttura Base

```python
from cat.experimental.form import form, CatForm
from pydantic import BaseModel

# 1. Definisci la struttura dati
class MieiDati(BaseModel):
    nome: str
    email: str
    eta: int

# 2. Crea il form
@form
class MioForm(CatForm):
    description = "Raccoglie informazioni utente"
    model_class = MieiDati
    start_examples = ["registrami", "voglio registrarmi"]
    stop_examples = ["annulla", "stop"]
    
    def submit(self, form_data):
        # Cosa fare quando il form è completo
        return f"Registrazione completata per {form_data['nome']}!"
```

### Componenti Principali

#### 1. **Model Class** (Pydantic)
Definisce la struttura dati da raccogliere:

```python
from pydantic import BaseModel, Field, validator

class OrderData(BaseModel):
    prodotto: str = Field(description="Nome del prodotto da ordinare")
    quantita: int = Field(gt=0, description="Quantità (deve essere > 0)")
    indirizzo: str = Field(min_length=10, description="Indirizzo di consegna")
    telefono: str = Field(regex=r'^\d{10}$', description="Numero di telefono (10 cifre)")
    
    @validator('prodotto')
    def validate_prodotto(cls, v):
        prodotti_disponibili = ['pizza', 'pasta', 'gelato']
        if v.lower() not in prodotti_disponibili:
            raise ValueError(f'Prodotto non disponibile. Scegli tra: {prodotti_disponibili}')
        return v.lower()
```

#### 2. **Trigger Examples**
Frasi che attivano il form:

```python
@form
class PizzaOrderForm(CatForm):
    start_examples = [
        "voglio ordinare una pizza",
        "ordina pizza",
        "fai un ordine",
        "ho fame"
    ]
    stop_examples = [
        "annulla ordine",
        "non voglio più",
        "stop",
        "basta"
    ]
```

#### 3. **Submit Method**
Cosa fare quando il form è completato:

```python
def submit(self, form_data):
    """Chiamato quando tutti i campi sono validi."""
    
    # Salva nel database
    save_order_to_db(form_data)
    
    # Invia email di conferma
    send_confirmation_email(form_data['email'])
    
    # Restituisci messaggio di successo
    return {
        "output": f"Ordine #{generate_order_id()} confermato! Consegna prevista in 30 minuti."
    }
```

## 📚 Esempi Pratici

### 1. 🍕 Form Ordine Pizza

```python
from cat.experimental.form import form, CatForm
from pydantic import BaseModel, Field, validator
from typing import Literal

class PizzaOrder(BaseModel):
    tipo_pizza: Literal['margherita', 'marinara', 'quattro_stagioni', 'diavola'] = Field(
        description="Tipo di pizza disponibile"
    )
    dimensione: Literal['piccola', 'media', 'grande'] = Field(
        description="Dimensione della pizza"
    )
    indirizzo: str = Field(min_length=10, description="Indirizzo di consegna completo")
    telefono: str = Field(regex=r'^\d{10}$', description="Numero di telefono (10 cifre)")
    note: str = Field(default="", description="Note aggiuntive (opzionale)")

@form
class PizzaOrderForm(CatForm):
    description = "Ordine Pizza a Domicilio"
    model_class = PizzaOrder
    start_examples = [
        "voglio ordinare una pizza",
        "ordina pizza",
        "ho voglia di pizza",
        "pizza a domicilio"
    ]
    stop_examples = [
        "annulla ordine",
        "non voglio più la pizza",
        "stop ordine"
    ]
    ask_confirm = True  # Chiedi conferma prima di inviare
    
    def submit(self, form_data):
        # Calcola prezzo
        prezzi = {
            'piccola': {'margherita': 6, 'marinara': 5, 'quattro_stagioni': 8, 'diavola': 7},
            'media': {'margherita': 8, 'marinara': 7, 'quattro_stagioni': 10, 'diavola': 9},
            'grande': {'margherita': 10, 'marinara': 9, 'quattro_stagioni': 12, 'diavola': 11}
        }
        
        prezzo = prezzi[form_data['dimensione']][form_data['tipo_pizza']]
        
        # Simula salvataggio ordine
        order_id = f"ORD{hash(str(form_data)) % 10000:04d}"
        
        return {
            "output": f"""🍕 Ordine Confermato!
            
Ordine: #{order_id}
Pizza: {form_data['tipo_pizza'].title()} ({form_data['dimensione']})
Prezzo: €{prezzo}
Indirizzo: {form_data['indirizzo']}
Telefono: {form_data['telefono']}
Note: {form_data['note'] or 'Nessuna'}

⏰ Tempo di consegna stimato: 30-45 minuti
📞 Ti chiameremo al {form_data['telefono']} per confermare l'arrivo!"""
        }
```

### 2. 📅 Form Prenotazione Appuntamento

```python
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator

class AppointmentData(BaseModel):
    nome: str = Field(min_length=2, description="Nome completo")
    email: str = Field(regex=r'^[^@]+@[^@]+\.[^@]+$', description="Email valida")
    servizio: Literal['consulenza', 'revisione', 'riparazione'] = Field(
        description="Tipo di servizio richiesto"
    )
    data: str = Field(description="Data appuntamento (formato: YYYY-MM-DD)")
    ora: str = Field(description="Ora appuntamento (formato: HH:MM)")
    note: str = Field(default="", description="Note aggiuntive")
    
    @validator('data')
    def validate_data(cls, v):
        try:
            date = datetime.strptime(v, '%Y-%m-%d').date()
            if date < datetime.now().date():
                raise ValueError("Non puoi prenotare nel passato")
            if date > (datetime.now().date() + timedelta(days=90)):
                raise ValueError("Non puoi prenotare oltre 90 giorni")
            return v
        except ValueError as e:
            if "does not match format" in str(e):
                raise ValueError("Formato data non valido. Usa: YYYY-MM-DD")
            raise e
    
    @validator('ora')
    def validate_ora(cls, v):
        try:
            time = datetime.strptime(v, '%H:%M').time()
            if time < datetime.strptime('09:00', '%H:%M').time():
                raise ValueError("Orario troppo presto (apriamo alle 09:00)")
            if time > datetime.strptime('18:00', '%H:%M').time():
                raise ValueError("Orario troppo tardi (chiudiamo alle 18:00)")
            return v
        except ValueError as e:
            if "does not match format" in str(e):
                raise ValueError("Formato ora non valido. Usa: HH:MM")
            raise e

@form
class AppointmentForm(CatForm):
    description = "Prenotazione Appuntamento"
    model_class = AppointmentData
    start_examples = [
        "voglio prenotare un appuntamento",
        "prenota appuntamento",
        "ho bisogno di un appuntamento",
        "quando siete liberi?"
    ]
    
    def submit(self, form_data):
        # Simula controllo disponibilità
        appointment_id = f"APP{hash(str(form_data)) % 10000:04d}"
        
        return {
            "output": f"""📅 Appuntamento Prenotato!
            
ID Prenotazione: #{appointment_id}
Nome: {form_data['nome']}
Email: {form_data['email']}
Servizio: {form_data['servizio'].title()}
Data e Ora: {form_data['data']} alle {form_data['ora']}
Note: {form_data['note'] or 'Nessuna'}

✅ Ti abbiamo inviato una conferma via email.
📞 Ti chiameremo il giorno prima per ricordarti l'appuntamento."""
        }
```

### 3. 🎫 Form Supporto Tecnico

```python
from pydantic import BaseModel, Field
from typing import Literal, Optional

class SupportTicket(BaseModel):
    nome: str = Field(description="Il tuo nome")
    email: str = Field(regex=r'^[^@]+@[^@]+\.[^@]+$', description="Email per contatto")
    categoria: Literal['bug', 'feature', 'supporto', 'altro'] = Field(
        description="Categoria del problema"
    )
    priorita: Literal['bassa', 'media', 'alta', 'critica'] = Field(
        description="Priorità del problema"
    )
    titolo: str = Field(min_length=5, max_length=100, description="Titolo breve del problema")
    descrizione: str = Field(min_length=20, description="Descrizione dettagliata del problema")
    sistema_operativo: Optional[str] = Field(default="", description="Sistema operativo (opzionale)")

@form
class SupportForm(CatForm):
    description = "Ticket di Supporto Tecnico"
    model_class = SupportTicket
    start_examples = [
        "ho un problema",
        "supporto tecnico",
        "segnala bug",
        "aiuto",
        "non funziona"
    ]
    
    def submit(self, form_data):
        # Genera ticket ID
        ticket_id = f"TKT{hash(str(form_data)) % 100000:05d}"
        
        # Stima tempo di risposta basato su priorità
        response_times = {
            'critica': '2 ore',
            'alta': '4 ore', 
            'media': '24 ore',
            'bassa': '48 ore'
        }
        
        response_time = response_times[form_data['priorita']]
        
        return {
            "output": f"""🎫 Ticket di Supporto Creato!
            
Ticket ID: #{ticket_id}
Categoria: {form_data['categoria'].title()}
Priorità: {form_data['priorita'].title()}
Titolo: {form_data['titolo']}

👤 Assegnato al team di supporto
⏰ Tempo di risposta stimato: {response_time}
📧 Riceverai aggiornamenti via email a: {form_data['email']}

Grazie per la segnalazione, {form_data['nome']}!"""
        }
```

## 🎨 Funzionalità Avanzate

### 1. **Validazione Personalizzata**

```python
@form
class AdvancedForm(CatForm):
    # ... altre configurazioni ...
    
    def validate_field(self, field_name, field_value, cat):
        """Validazione personalizzata per ogni campo."""
        
        if field_name == "email":
            # Controlla se email già esistente
            if email_exists_in_db(field_value):
                return "Questa email è già registrata. Usa un'altra email."
        
        elif field_name == "username":
            # Controlla username disponibile
            if not is_username_available(field_value):
                return "Username non disponibile. Scegline un altro."
        
        # Se tutto ok, restituisci None
        return None
```

### 2. **Messaggi Personalizzati**

```python
@form
class CustomMessagesForm(CatForm):
    # ... altre configurazioni ...
    
    def message_missing_fields(self, missing_fields, cat):
        """Messaggio personalizzato per campi mancanti."""
        if len(missing_fields) == 1:
            return f"Mi serve ancora: {missing_fields[0]}"
        else:
            return f"Mi servono ancora: {', '.join(missing_fields[:-1])} e {missing_fields[-1]}"
    
    def message_invalid_field(self, field_name, field_value, error, cat):
        """Messaggio personalizzato per errori di validazione."""
        friendly_names = {
            'email': 'indirizzo email',
            'telefono': 'numero di telefono',
            'data': 'data'
        }
        
        field_display = friendly_names.get(field_name, field_name)
        return f"Il {field_display} '{field_value}' non è valido: {error}"
```

### 3. **Form Condizionali**

```python
class ConditionalData(BaseModel):
    tipo_utente: Literal['privato', 'azienda']
    nome: str
    # Campi condizionali
    codice_fiscale: Optional[str] = None
    partita_iva: Optional[str] = None
    
    @validator('codice_fiscale')
    def validate_cf(cls, v, values):
        if values.get('tipo_utente') == 'privato' and not v:
            raise ValueError("Codice fiscale richiesto per utenti privati")
        return v
    
    @validator('partita_iva')
    def validate_piva(cls, v, values):
        if values.get('tipo_utente') == 'azienda' and not v:
            raise ValueError("Partita IVA richiesta per aziende")
        return v

@form
class ConditionalForm(CatForm):
    model_class = ConditionalData
    # ... altre configurazioni ...
    
    def ask_field(self, field_name, cat):
        """Personalizza come vengono richiesti i campi."""
        
        current_data = self.get_current_data()
        
        if field_name == 'codice_fiscale' and current_data.get('tipo_utente') != 'privato':
            return None  # Non chiedere questo campo
        
        if field_name == 'partita_iva' and current_data.get('tipo_utente') != 'azienda':
            return None  # Non chiedere questo campo
        
        # Usa il comportamento default per altri campi
        return super().ask_field(field_name, cat)
```

## 🔧 Best Practices per Forms

### 1. **Gestione Errori Robusta**

```python
@form
class RobustForm(CatForm):
    # ... configurazioni ...
    
    def submit(self, form_data):
        try:
            # Operazioni principali
            result = process_form_data(form_data)
            
            return {"output": f"Successo: {result}"}
            
        except DatabaseError as e:
            # Errore database - riprova più tardi
            return {"output": "Errore temporaneo. Riprova tra qualche minuto."}
            
        except ValidationError as e:
            # Errore validazione - chiedi di correggere
            return {"output": f"Dati non validi: {e}. Correggi e riprova."}
            
        except Exception as e:
            # Errore generico
            self.cat.log.error(f"Errore in form submit: {e}")
            return {"output": "Si è verificato un errore. Contatta il supporto."}
```

### 2. **User Experience Ottimale**

```python
@form
class UserFriendlyForm(CatForm):
    # ... configurazioni ...
    
    def message_welcome(self, cat):
        """Messaggio di benvenuto personalizzato."""
        return """👋 Ciao! Ti aiuto a completare la registrazione.
        
Ti farò alcune domande. Puoi scrivere 'stop' in qualsiasi momento per annullare."""
    
    def message_confirm(self, form_data, cat):
        """Messaggio di conferma personalizzato."""
        return f"""📋 Riepilogo dei tuoi dati:
        
Nome: {form_data['nome']}
Email: {form_data['email']}

✅ Confermi che i dati sono corretti? (sì/no)"""
```

### 3. **Sicurezza e Privacy**

```python
@form
class SecureForm(CatForm):
    # ... configurazioni ...
    
    def submit(self, form_data):
        # Sanitizza i dati
        sanitized_data = sanitize_form_data(form_data)
        
        # Log senza dati sensibili
        self.cat.log.info(f"Form submitted by user {self.cat.user_id}")
        
        # Cripta dati sensibili prima del salvataggio
        encrypted_data = encrypt_sensitive_fields(sanitized_data)
        
        # Salva in modo sicuro
        save_to_secure_db(encrypted_data)
        
        return {"output": "Dati salvati in modo sicuro!"}
```

## 🎯 Prossimi Passi

Ora che padroneggi i Forms:

1. **🧪 Crea il tuo form**: Inizia con qualcosa di semplice
2. **🔗 Combina tutto**: Usa Tools, Hooks e Forms insieme
3. **🚀 Esempi avanzati**: [07-esempi-avanzati.md](07-esempi-avanzati.md)

---

**Pronto per progetti complessi?** Vai al prossimo capitolo: [07-esempi-avanzati.md](07-esempi-avanzati.md) 🚀
