# 🚀 Installazione di Cheshire Cat AI

Questa guida ti porterà dall'installazione alla prima conversazione con il tuo Cheshire Cat in pochi minuti!

## 📋 Prerequisiti

### Opzione 1: Docker (Raccomandato) 🐳
- **Docker** e **Docker Compose** installati
- **4GB RAM** disponibili
- **2GB spazio disco**

### Opzione 2: Installazione Manuale 🛠️
- **Python 3.10+**
- **Git**
- **8GB RAM** (per modelli locali)

## 🐳 Installazione con Docker (Raccomandato)

### Passo 1: Clona il Repository

```bash
# Clona il repository ufficiale
git clone https://github.com/cheshire-cat-ai/core.git cheshire-cat-ai
cd cheshire-cat-ai
```

### Passo 2: Avvia il Cat

```bash
# Avvia tutti i servizi
docker-compose up

# Oppure in background
docker-compose up -d
```

### Passo 3: Verifica l'Installazione

Apri il browser e vai su:
- **Admin Portal**: http://localhost:1865/admin
- **API Docs**: http://localhost:1865/docs

🎉 **Congratulazioni!** Il tuo Cheshire Cat è online!

## 🛠️ Installazione Manuale

### Passo 1: Clona e Prepara

```bash
# Clona il repository
git clone https://github.com/cheshire-cat-ai/core.git cheshire-cat-ai
cd cheshire-cat-ai

# Crea ambiente virtuale
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# Installa dipendenze
pip install -r requirements.txt
```

### Passo 2: Configura il Database

```bash
# Il Cat userà SQLite di default
# Per PostgreSQL, modifica le variabili d'ambiente
```

### Passo 3: Avvia il Cat

```bash
python main.py
```

## ⚙️ Prima Configurazione

### 1. Accedi all'Admin Portal

Vai su http://localhost:1865/admin

### 2. Configura il Language Model

Il Cat ha bisogno di un LLM per funzionare. Hai diverse opzioni:

#### Opzione A: OpenAI (Facile) 💰
```json
{
  "provider": "openai",
  "api_key": "sk-your-api-key-here",
  "model": "gpt-3.5-turbo"
}
```

#### Opzione B: Ollama (Locale, Gratuito) 🏠
```bash
# Installa Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Scarica un modello
ollama pull llama2

# Configura nel Cat
{
  "provider": "ollama",
  "base_url": "http://localhost:11434",
  "model": "llama2"
}
```

#### Opzione C: Hugging Face (Gratuito) 🤗
```json
{
  "provider": "huggingface",
  "api_key": "hf_your-token-here",
  "model": "microsoft/DialoGPT-medium"
}
```

### 3. Configura l'Embedder

Per la memoria vettoriale, configura un embedder:

```json
{
  "provider": "openai",
  "api_key": "sk-your-api-key-here",
  "model": "text-embedding-ada-002"
}
```

## 🧪 Prima Conversazione

### Via Web Interface

1. Vai su http://localhost:1865/admin
2. Clicca su "Chat" nella sidebar
3. Scrivi: "Ciao, come ti chiami?"

### Via API

```bash
curl -X POST "http://localhost:1865/message" \
  -H "Content-Type: application/json" \
  -d '{"text": "Ciao, come ti chiami?"}'
```

### Via WebSocket

```javascript
const ws = new WebSocket('ws://localhost:1865/ws');
ws.onopen = () => {
  ws.send(JSON.stringify({
    text: "Ciao, come ti chiami?"
  }));
};
```

## 📁 Struttura delle Directory

Dopo l'installazione, avrai questa struttura:

```
cheshire-cat-ai/
├── cat/                    # Core del framework
│   ├── plugins/           # I tuoi plugin vanno qui
│   ├── data/             # Database e file
│   └── static/           # File statici admin
├── docker-compose.yml    # Configurazione Docker
├── main.py              # Entry point
└── requirements.txt     # Dipendenze Python
```

## 🔧 Configurazioni Avanzate

### Variabili d'Ambiente

Crea un file `.env`:

```bash
# Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=cheshire_cat
POSTGRES_USER=cat
POSTGRES_PASSWORD=password

# Sicurezza
API_KEY=your-secret-api-key
CORS_ALLOWED_ORIGINS=["http://localhost:3000"]

# Logging
LOG_LEVEL=INFO
```

### Docker Compose Personalizzato

```yaml
version: '3.8'
services:
  cheshire-cat-core:
    image: ghcr.io/cheshire-cat-ai/core:latest
    container_name: cheshire_cat_core
    ports:
      - "1865:80"
    volumes:
      - ./cat/plugins:/app/cat/plugins
      - ./cat/data:/app/cat/data
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=cheshire_cat
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    container_name: cheshire_cat_db
    environment:
      POSTGRES_DB: cheshire_cat
      POSTGRES_USER: cat
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 🐛 Risoluzione Problemi

### Problema: "Port 1865 already in use"
```bash
# Trova il processo che usa la porta
lsof -i :1865

# Termina il processo
kill -9 <PID>
```

### Problema: "Out of memory"
```bash
# Aumenta la memoria Docker
# Docker Desktop > Settings > Resources > Memory > 4GB+
```

### Problema: "LLM not responding"
1. Verifica la configurazione API
2. Controlla i log: `docker-compose logs cheshire-cat-core`
3. Testa la connessione API esternamente

## 📊 Monitoraggio

### Log in Tempo Reale
```bash
# Docker
docker-compose logs -f cheshire-cat-core

# Installazione manuale
tail -f cat/log/cat.log
```

### Metriche di Sistema
```bash
# Uso memoria
docker stats cheshire-cat-core

# Spazio disco
du -sh cat/data/
```

## 🎯 Prossimi Passi

Ora che il tuo Cat è installato e configurato:

1. **🧪 Prova le funzionalità base**: [03-concetti-base.md](03-concetti-base.md)
2. **📚 Carica il primo documento**: Usa l'Admin Portal
3. **🔨 Crea il primo plugin**: [04-plugin-tools.md](04-plugin-tools.md)

---

**Tutto funziona?** Perfetto! Vai al prossimo capitolo: [03-concetti-base.md](03-concetti-base.md) 🚀

## 🆘 Supporto

- **Discord**: [Community Discord](https://discord.gg/bHX5sNFCYU)
- **GitHub Issues**: [Segnala problemi](https://github.com/cheshire-cat-ai/core/issues)
- **Documentazione**: [Docs ufficiali](https://cheshire-cat-ai.github.io/docs/)
