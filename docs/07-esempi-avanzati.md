# 🚀 Esempi Avanzati - Progetti Completi

Questa guida ti mostrerà come creare plugin complessi combinando Tools, Hooks e Forms per costruire applicazioni reali e potenti.

## 🎯 Progetti Completi

### 1. 🏪 E-commerce Assistant

Un assistente completo per un negozio online che gestisce prodotti, ordini e clienti.

#### Struttura del Plugin

```
ecommerce_assistant/
├── plugin.json
├── main.py              # Tools e Forms principali
├── hooks.py            # Personalizzazioni comportamento
├── models.py           # Modelli dati Pydantic
├── database.py         # Gestione database
├── utils.py            # Funzioni di utilità
└── requirements.txt    # Dipendenze
```

#### models.py
```python
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Literal
from datetime import datetime

class Product(BaseModel):
    id: str
    name: str
    price: float = Field(gt=0)
    category: str
    stock: int = Field(ge=0)
    description: str

class Customer(BaseModel):
    id: str
    name: str
    email: str = Field(regex=r'^[^@]+@[^@]+\.[^@]+$')
    phone: str
    address: str
    vip_status: bool = False

class OrderItem(BaseModel):
    product_id: str
    quantity: int = Field(gt=0)
    price: float

class Order(BaseModel):
    id: str
    customer_id: str
    items: List[OrderItem]
    total: float
    status: Literal['pending', 'confirmed', 'shipped', 'delivered', 'cancelled']
    created_at: datetime
    notes: Optional[str] = None
```

#### database.py
```python
import json
import os
from typing import List, Optional
from .models import Product, Customer, Order

class EcommerceDB:
    def __init__(self, data_dir="cat/data/ecommerce"):
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
    def _load_data(self, filename: str) -> List[dict]:
        filepath = os.path.join(self.data_dir, filename)
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                return json.load(f)
        return []
    
    def _save_data(self, filename: str, data: List[dict]):
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2, default=str)
    
    # Products
    def get_products(self, category: Optional[str] = None) -> List[Product]:
        products_data = self._load_data('products.json')
        products = [Product(**p) for p in products_data]
        
        if category:
            products = [p for p in products if p.category.lower() == category.lower()]
        
        return products
    
    def search_products(self, query: str) -> List[Product]:
        products = self.get_products()
        query = query.lower()
        
        return [p for p in products 
                if query in p.name.lower() or query in p.description.lower()]
    
    def get_product(self, product_id: str) -> Optional[Product]:
        products = self.get_products()
        return next((p for p in products if p.id == product_id), None)
    
    # Customers
    def get_customer(self, customer_id: str) -> Optional[Customer]:
        customers_data = self._load_data('customers.json')
        customer_data = next((c for c in customers_data if c['id'] == customer_id), None)
        return Customer(**customer_data) if customer_data else None
    
    def save_customer(self, customer: Customer):
        customers_data = self._load_data('customers.json')
        # Update existing or add new
        for i, c in enumerate(customers_data):
            if c['id'] == customer.id:
                customers_data[i] = customer.dict()
                break
        else:
            customers_data.append(customer.dict())
        
        self._save_data('customers.json', customers_data)
    
    # Orders
    def save_order(self, order: Order):
        orders_data = self._load_data('orders.json')
        orders_data.append(order.dict())
        self._save_data('orders.json', orders_data)
    
    def get_customer_orders(self, customer_id: str) -> List[Order]:
        orders_data = self._load_data('orders.json')
        customer_orders = [o for o in orders_data if o['customer_id'] == customer_id]
        return [Order(**o) for o in customer_orders]
```

#### main.py - Tools
```python
from cat.mad_hatter.decorators import tool, form
from cat.experimental.form import CatForm
from .database import EcommerceDB
from .models import Customer, OrderItem
from pydantic import BaseModel
import uuid
from datetime import datetime

db = EcommerceDB()

@tool
def cerca_prodotti(query, cat):
    """Cerca prodotti nel catalogo.
    
    Input: termine di ricerca (es: "smartphone", "scarpe")
    """
    try:
        products = db.search_products(query)
        
        if not products:
            return f"Nessun prodotto trovato per '{query}'"
        
        result = f"🛍️ Prodotti trovati per '{query}':\n\n"
        
        for product in products[:5]:  # Limita a 5 risultati
            stock_status = "✅ Disponibile" if product.stock > 0 else "❌ Esaurito"
            result += f"**{product.name}**\n"
            result += f"💰 €{product.price:.2f}\n"
            result += f"📦 {stock_status} ({product.stock} pz)\n"
            result += f"📝 {product.description[:100]}...\n"
            result += f"🏷️ ID: {product.id}\n\n"
        
        return result
        
    except Exception as e:
        return f"Errore nella ricerca: {str(e)}"

@tool
def info_prodotto(product_id, cat):
    """Ottiene informazioni dettagliate su un prodotto.
    
    Input: ID del prodotto
    """
    try:
        product = db.get_product(product_id.strip())
        
        if not product:
            return f"Prodotto con ID '{product_id}' non trovato"
        
        stock_status = "✅ Disponibile" if product.stock > 0 else "❌ Esaurito"
        
        return f"""🛍️ **{product.name}**

💰 **Prezzo**: €{product.price:.2f}
🏷️ **Categoria**: {product.category}
📦 **Disponibilità**: {stock_status} ({product.stock} pezzi)
📝 **Descrizione**: {product.description}
🆔 **ID Prodotto**: {product.id}

Per ordinare questo prodotto, scrivi: "voglio ordinare" """
        
    except Exception as e:
        return f"Errore nel recuperare le informazioni: {str(e)}"

@tool
def stato_ordine(order_id, cat):
    """Controlla lo stato di un ordine.
    
    Input: ID dell'ordine
    """
    try:
        # Simula controllo ordine
        orders_data = db._load_data('orders.json')
        order_data = next((o for o in orders_data if o['id'] == order_id), None)
        
        if not order_data:
            return f"Ordine '{order_id}' non trovato"
        
        status_messages = {
            'pending': '⏳ In attesa di conferma',
            'confirmed': '✅ Confermato - In preparazione',
            'shipped': '🚚 Spedito - In consegna',
            'delivered': '📦 Consegnato',
            'cancelled': '❌ Annullato'
        }
        
        status_msg = status_messages.get(order_data['status'], 'Stato sconosciuto')
        
        return f"""📋 **Ordine #{order_data['id']}**

📊 **Stato**: {status_msg}
📅 **Data ordine**: {order_data['created_at'][:10]}
💰 **Totale**: €{order_data['total']:.2f}
📝 **Note**: {order_data.get('notes', 'Nessuna')}

Per assistenza, contatta il supporto clienti."""
        
    except Exception as e:
        return f"Errore nel controllo ordine: {str(e)}"
```

#### main.py - Forms
```python
# Continuazione di main.py

class OrderFormData(BaseModel):
    customer_name: str = Field(description="Nome completo del cliente")
    customer_email: str = Field(regex=r'^[^@]+@[^@]+\.[^@]+$', description="Email del cliente")
    customer_phone: str = Field(description="Numero di telefono")
    customer_address: str = Field(min_length=10, description="Indirizzo di consegna completo")
    product_ids: str = Field(description="ID prodotti separati da virgola (es: prod1,prod2)")
    quantities: str = Field(description="Quantità per ogni prodotto (es: 2,1)")
    notes: str = Field(default="", description="Note aggiuntive per l'ordine")

@form
class OrderForm(CatForm):
    description = "Creazione Nuovo Ordine"
    model_class = OrderFormData
    start_examples = [
        "voglio ordinare",
        "fai un ordine",
        "compra prodotto",
        "acquista"
    ]
    stop_examples = [
        "annulla ordine",
        "non voglio più ordinare"
    ]
    ask_confirm = True
    
    def submit(self, form_data):
        try:
            # Parse prodotti e quantità
            product_ids = [pid.strip() for pid in form_data['product_ids'].split(',')]
            quantities = [int(q.strip()) for q in form_data['quantities'].split(',')]
            
            if len(product_ids) != len(quantities):
                return {"output": "Errore: numero di prodotti e quantità non corrispondono"}
            
            # Verifica prodotti e calcola totale
            order_items = []
            total = 0
            
            for product_id, quantity in zip(product_ids, quantities):
                product = db.get_product(product_id)
                if not product:
                    return {"output": f"Prodotto '{product_id}' non trovato"}
                
                if product.stock < quantity:
                    return {"output": f"Stock insufficiente per {product.name} (disponibili: {product.stock})"}
                
                item_total = product.price * quantity
                order_items.append(OrderItem(
                    product_id=product_id,
                    quantity=quantity,
                    price=item_total
                ))
                total += item_total
            
            # Crea/aggiorna cliente
            customer_id = str(uuid.uuid4())
            customer = Customer(
                id=customer_id,
                name=form_data['customer_name'],
                email=form_data['customer_email'],
                phone=form_data['customer_phone'],
                address=form_data['customer_address']
            )
            db.save_customer(customer)
            
            # Crea ordine
            order_id = f"ORD{datetime.now().strftime('%Y%m%d')}{hash(str(form_data)) % 1000:03d}"
            order = Order(
                id=order_id,
                customer_id=customer_id,
                items=order_items,
                total=total,
                status='pending',
                created_at=datetime.now(),
                notes=form_data['notes']
            )
            db.save_order(order)
            
            # Aggiorna stock (simulato)
            # In un'app reale, questo dovrebbe essere atomico
            
            return {"output": f"""🎉 **Ordine Creato con Successo!**

📋 **Ordine**: #{order_id}
👤 **Cliente**: {customer.name}
📧 **Email**: {customer.email}
💰 **Totale**: €{total:.2f}
📦 **Stato**: In attesa di conferma

📧 Ti abbiamo inviato una conferma via email.
📞 Ti contatteremo al {customer.phone} per confermare la consegna.

Per controllare lo stato dell'ordine, scrivi: "stato ordine {order_id}" """}
            
        except Exception as e:
            return {"output": f"Errore nella creazione dell'ordine: {str(e)}"}
```

#### hooks.py
```python
from cat.mad_hatter.decorators import hook

@hook(priority=10)
def agent_prompt_prefix(prefix, cat):
    """Trasforma il Cat in un assistente e-commerce."""
    
    prefix = """Sei un assistente e-commerce esperto e amichevole.
    
Il tuo ruolo è aiutare i clienti a:
- Trovare prodotti nel catalogo
- Ottenere informazioni dettagliate sui prodotti
- Creare ordini in modo semplice e veloce
- Controllare lo stato degli ordini
- Risolvere problemi e domande

Sei sempre cortese, professionale e proattivo nel suggerire prodotti correlati.
Quando un cliente mostra interesse per un prodotto, offri sempre di aiutarlo a ordinare.
"""
    
    return prefix

@hook
def before_cat_sends_message(message, cat):
    """Personalizza i messaggi per l'e-commerce."""
    
    content = message["content"]
    
    # Aggiungi suggerimenti contestuali
    if "prodotto" in content.lower() and "trovato" in content.lower():
        content += "\n\n💡 **Suggerimento**: Scrivi 'voglio ordinare' per procedere con l'acquisto!"
    
    elif "ordine" in content.lower() and "creato" in content.lower():
        content += "\n\n🛍️ **Continua a comprare**: Scrivi 'cerca prodotti' per vedere altri articoli!"
    
    message["content"] = content
    return message

@hook
def before_cat_recalls_memories(query, cat):
    """Migliora le ricerche per l'e-commerce."""
    
    # Espandi termini di ricerca comuni
    ecommerce_synonyms = {
        "telefono": ["smartphone", "cellulare", "mobile"],
        "scarpe": ["calzature", "sneakers", "sandali"],
        "vestiti": ["abbigliamento", "magliette", "pantaloni"],
        "computer": ["pc", "laptop", "notebook"]
    }
    
    query_lower = query.lower()
    for term, synonyms in ecommerce_synonyms.items():
        if term in query_lower:
            query += " " + " ".join(synonyms)
    
    return query
```

### 2. 📚 Knowledge Management System

Un sistema completo per gestire documenti, FAQ e knowledge base aziendale.

#### Struttura del Plugin

```python
# knowledge_manager/main.py

from cat.mad_hatter.decorators import tool, hook, form
from cat.experimental.form import CatForm
from pydantic import BaseModel, Field
import os
import json
from datetime import datetime
from typing import List, Optional

class FAQItem(BaseModel):
    id: str
    question: str
    answer: str
    category: str
    tags: List[str]
    created_at: datetime
    updated_at: datetime
    views: int = 0

class DocumentMeta(BaseModel):
    filename: str
    category: str
    tags: List[str]
    summary: str
    uploaded_at: datetime

@tool
def cerca_faq(query, cat):
    """Cerca nelle FAQ aziendali.
    
    Input: termine di ricerca o domanda
    """
    try:
        faq_file = "cat/data/knowledge/faq.json"
        
        if not os.path.exists(faq_file):
            return "Nessuna FAQ disponibile al momento."
        
        with open(faq_file, 'r') as f:
            faqs_data = json.load(f)
        
        faqs = [FAQItem(**faq) for faq in faqs_data]
        
        # Ricerca fuzzy nelle domande e risposte
        query_lower = query.lower()
        matching_faqs = []
        
        for faq in faqs:
            score = 0
            if query_lower in faq.question.lower():
                score += 3
            if query_lower in faq.answer.lower():
                score += 2
            if any(tag.lower() in query_lower for tag in faq.tags):
                score += 1
            
            if score > 0:
                matching_faqs.append((faq, score))
        
        # Ordina per rilevanza
        matching_faqs.sort(key=lambda x: x[1], reverse=True)
        
        if not matching_faqs:
            return f"Nessuna FAQ trovata per '{query}'. Prova con termini diversi."
        
        result = f"❓ **FAQ trovate per '{query}':**\n\n"
        
        for faq, score in matching_faqs[:3]:  # Top 3 risultati
            result += f"**Q: {faq.question}**\n"
            result += f"**A:** {faq.answer}\n"
            result += f"🏷️ Categoria: {faq.category}\n"
            result += f"👀 Visualizzazioni: {faq.views}\n\n"
            
            # Incrementa views (simulato)
            faq.views += 1
        
        return result
        
    except Exception as e:
        return f"Errore nella ricerca FAQ: {str(e)}"

@tool
def statistiche_knowledge(input, cat):
    """Mostra statistiche del sistema di knowledge management."""
    
    try:
        stats = {
            "documenti_totali": 0,
            "faq_totali": 0,
            "categorie": set(),
            "documenti_recenti": 0
        }
        
        # Conta documenti
        docs_dir = "cat/data/knowledge/documents"
        if os.path.exists(docs_dir):
            stats["documenti_totali"] = len([f for f in os.listdir(docs_dir) if f.endswith('.json')])
        
        # Conta FAQ
        faq_file = "cat/data/knowledge/faq.json"
        if os.path.exists(faq_file):
            with open(faq_file, 'r') as f:
                faqs = json.load(f)
                stats["faq_totali"] = len(faqs)
                stats["categorie"].update(faq.get('category', '') for faq in faqs)
        
        return f"""📊 **Statistiche Knowledge Base**

📄 **Documenti**: {stats['documenti_totali']}
❓ **FAQ**: {stats['faq_totali']}
🏷️ **Categorie**: {len(stats['categorie'])}
📈 **Documenti recenti**: {stats['documenti_recenti']} (ultimi 7 giorni)

🔍 **Comandi disponibili**:
- "cerca faq [termine]" - Cerca nelle FAQ
- "aggiungi faq" - Aggiungi nuova FAQ
- "categorie knowledge" - Vedi tutte le categorie"""
        
    except Exception as e:
        return f"Errore nel recuperare statistiche: {str(e)}"

# Form per aggiungere FAQ
class FAQFormData(BaseModel):
    question: str = Field(min_length=10, description="Domanda frequente")
    answer: str = Field(min_length=20, description="Risposta dettagliata")
    category: str = Field(description="Categoria (es: tecnico, vendite, supporto)")
    tags: str = Field(description="Tag separati da virgola (es: login,password,accesso)")

@form
class AddFAQForm(CatForm):
    description = "Aggiungi nuova FAQ"
    model_class = FAQFormData
    start_examples = [
        "aggiungi faq",
        "nuova faq",
        "crea domanda frequente"
    ]
    
    def submit(self, form_data):
        try:
            # Crea directory se non esiste
            os.makedirs("cat/data/knowledge", exist_ok=True)
            faq_file = "cat/data/knowledge/faq.json"
            
            # Carica FAQ esistenti
            if os.path.exists(faq_file):
                with open(faq_file, 'r') as f:
                    faqs = json.load(f)
            else:
                faqs = []
            
            # Crea nuova FAQ
            new_faq = FAQItem(
                id=f"faq_{len(faqs) + 1}_{hash(form_data['question']) % 1000:03d}",
                question=form_data['question'],
                answer=form_data['answer'],
                category=form_data['category'],
                tags=[tag.strip() for tag in form_data['tags'].split(',')],
                created_at=datetime.now(),
                updated_at=datetime.now(),
                views=0
            )
            
            # Aggiungi e salva
            faqs.append(new_faq.dict())
            
            with open(faq_file, 'w') as f:
                json.dump(faqs, f, indent=2, default=str)
            
            return {"output": f"""✅ **FAQ Aggiunta con Successo!**

❓ **Domanda**: {new_faq.question}
💬 **Risposta**: {new_faq.answer[:100]}...
🏷️ **Categoria**: {new_faq.category}
🏷️ **Tag**: {', '.join(new_faq.tags)}
🆔 **ID**: {new_faq.id}

La FAQ è ora disponibile per le ricerche!"""}
            
        except Exception as e:
            return {"output": f"Errore nell'aggiungere la FAQ: {str(e)}"}

# Hook per migliorare le risposte con knowledge base
@hook
def before_cat_sends_message(message, cat):
    """Arricchisce le risposte con suggerimenti dalla knowledge base."""
    
    content = message["content"]
    
    # Se la risposta sembra incompleta, suggerisci di cercare nella KB
    if len(content) < 50 or "non so" in content.lower():
        content += "\n\n💡 **Suggerimento**: Prova a cercare nella knowledge base con 'cerca faq [termine]'"
    
    # Aggiungi link a FAQ correlate per argomenti comuni
    common_topics = {
        "login": "Problemi di accesso? Cerca 'faq login'",
        "password": "Problemi con la password? Cerca 'faq password'",
        "pagamento": "Domande sui pagamenti? Cerca 'faq pagamento'",
        "spedizione": "Info sulle spedizioni? Cerca 'faq spedizione'"
    }
    
    content_lower = content.lower()
    for topic, suggestion in common_topics.items():
        if topic in content_lower:
            content += f"\n\n🔗 {suggestion}"
            break
    
    message["content"] = content
    return message
```

## 🎯 Prossimi Passi

Hai visto esempi complessi che combinano tutti gli elementi di Cheshire Cat AI:

1. **🧪 Sperimenta**: Prova a modificare questi esempi
2. **🔨 Crea il tuo progetto**: Usa questi pattern per i tuoi casi d'uso
3. **🌟 Contribuisci**: Condividi i tuoi plugin con la community

---

**Congratulazioni!** 🎉 Ora hai tutte le conoscenze per creare plugin potenti e complessi con Cheshire Cat AI. Inizia a costruire il tuo assistente AI personalizzato!
