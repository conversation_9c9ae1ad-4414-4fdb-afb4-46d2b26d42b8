# 🧰 Plugin Tools - Dare Azioni al Tuo Cat

I **Tools** sono il modo per dare al tuo Cheshire Cat la capacità di eseguire azioni concrete. Pensa ai tools come "superpoteri" che aggiungi al tuo assistente AI.

## 🎯 Cosa Sono i Tools?

Un **Tool** è una funzione Python che:
- Il Language Model può **scegliere** di eseguire
- Riceve parametri dal LLM
- Esegue codice Python
- Restituisce un risultato al LLM

### Esempio Semplice

```python
from cat.mad_hatter.decorators import tool

@tool
def che_ore_sono(input, cat):
    """Dice che ore sono adesso."""
    from datetime import datetime
    now = datetime.now()
    return f"Sono le {now.strftime('%H:%M')} del {now.strftime('%d/%m/%Y')}"
```

Quando l'utente chiede "Che ore sono?", il LLM:
1. **Riconosce** che serve il tool `che_ore_sono`
2. **Chiama** la funzione
3. **Riceve** il risultato
4. **Risponde** all'utente con l'informazione

## 🏗️ Anatomia di un Tool

### Struttura Base

```python
from cat.mad_hatter.decorators import tool

@tool
def nome_del_tool(input, cat):
    """Descrizione chiara di cosa fa il tool.
    
    Args:
        input: Parametri dal LLM (può essere stringa o dict)
        cat: Istanza StrayCat per accedere al framework
    
    Returns:
        str: Risultato da mostrare all'utente
    """
    # Il tuo codice qui
    return "Risultato del tool"
```

### Parametri Dettagliati

#### `input` - I Dati dal LLM
```python
@tool
def calcola_somma(input, cat):
    """Calcola la somma di due numeri.
    
    Input format: "numero1,numero2" (es: "5,3")
    """
    try:
        numeri = input.split(',')
        a = float(numeri[0].strip())
        b = float(numeri[1].strip())
        return f"La somma di {a} e {b} è {a + b}"
    except:
        return "Errore: fornisci due numeri separati da virgola"
```

#### `cat` - Accesso al Framework
```python
@tool
def salva_nota(input, cat):
    """Salva una nota nella memoria del Cat."""
    # Accedi alla working memory
    if 'note' not in cat.working_memory:
        cat.working_memory['note'] = []
    
    cat.working_memory['note'].append({
        'testo': input,
        'timestamp': datetime.now().isoformat()
    })
    
    return f"Nota salvata: {input}"
```

## 📚 Esempi Pratici

### 1. 🧮 Calcolatrice Avanzata

```python
from cat.mad_hatter.decorators import tool
import math

@tool
def calcolatrice(input, cat):
    """Esegue calcoli matematici complessi.
    
    Supporta: +, -, *, /, **, sqrt(), sin(), cos(), log()
    Esempio: "2 + 3 * 4" o "sqrt(16)"
    """
    try:
        # Sostituisci funzioni matematiche
        input = input.replace('sqrt', 'math.sqrt')
        input = input.replace('sin', 'math.sin')
        input = input.replace('cos', 'math.cos')
        input = input.replace('log', 'math.log')
        
        # Valuta l'espressione (attenzione: solo per input fidati!)
        risultato = eval(input)
        return f"Il risultato è: {risultato}"
    except Exception as e:
        return f"Errore nel calcolo: {str(e)}"
```

### 2. 🌤️ Meteo API

```python
from cat.mad_hatter.decorators import tool
import requests

@tool
def meteo_citta(input, cat):
    """Ottiene il meteo per una città specifica.
    
    Input: nome della città (es: "Roma", "Milano")
    """
    api_key = "YOUR_API_KEY"  # Sostituisci con la tua API key
    base_url = "http://api.openweathermap.org/data/2.5/weather"
    
    try:
        params = {
            'q': input,
            'appid': api_key,
            'units': 'metric',
            'lang': 'it'
        }
        
        response = requests.get(base_url, params=params)
        data = response.json()
        
        if response.status_code == 200:
            temp = data['main']['temp']
            desc = data['weather'][0]['description']
            return f"A {input}: {temp}°C, {desc}"
        else:
            return f"Non riesco a trovare il meteo per {input}"
            
    except Exception as e:
        return f"Errore nel recuperare il meteo: {str(e)}"
```

### 3. 📁 File Manager

```python
from cat.mad_hatter.decorators import tool
import os
import json

@tool
def lista_file(input, cat):
    """Lista i file in una directory.
    
    Input: percorso della directory (es: "/home/<USER>/Documents")
    """
    try:
        if not input or input.strip() == "":
            input = "."  # Directory corrente
            
        files = os.listdir(input)
        files_info = []
        
        for file in files[:10]:  # Limita a 10 file
            path = os.path.join(input, file)
            if os.path.isfile(path):
                size = os.path.getsize(path)
                files_info.append(f"📄 {file} ({size} bytes)")
            else:
                files_info.append(f"📁 {file}/")
        
        return f"File in {input}:\n" + "\n".join(files_info)
        
    except Exception as e:
        return f"Errore nell'accesso alla directory: {str(e)}"

@tool
def leggi_file(input, cat):
    """Legge il contenuto di un file di testo.
    
    Input: percorso del file (es: "/path/to/file.txt")
    """
    try:
        with open(input, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Limita la lunghezza per non sovraccaricare il LLM
        if len(content) > 1000:
            content = content[:1000] + "... (file troncato)"
            
        return f"Contenuto di {input}:\n{content}"
        
    except Exception as e:
        return f"Errore nella lettura del file: {str(e)}"
```

### 4. 🗄️ Database Query

```python
from cat.mad_hatter.decorators import tool
import sqlite3
import json

@tool
def query_database(input, cat):
    """Esegue una query SQL su un database SQLite.
    
    Input: query SQL (es: "SELECT * FROM users LIMIT 5")
    """
    db_path = "cat/data/app.db"  # Percorso del database
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Solo query SELECT per sicurezza
        if not input.strip().upper().startswith('SELECT'):
            return "Errore: sono permesse solo query SELECT"
        
        cursor.execute(input)
        results = cursor.fetchall()
        columns = [description[0] for description in cursor.description]
        
        conn.close()
        
        # Formatta i risultati
        if results:
            formatted_results = []
            for row in results[:10]:  # Limita a 10 righe
                row_dict = dict(zip(columns, row))
                formatted_results.append(row_dict)
            
            return f"Risultati query:\n{json.dumps(formatted_results, indent=2, ensure_ascii=False)}"
        else:
            return "Nessun risultato trovato"
            
    except Exception as e:
        return f"Errore nella query: {str(e)}"
```

## 🎨 Tool Avanzati

### 1. Tool con Parametri Strutturati

```python
from cat.mad_hatter.decorators import tool
import json

@tool
def crea_evento_calendario(input, cat):
    """Crea un evento nel calendario.
    
    Input JSON: {
        "titolo": "Nome evento",
        "data": "2024-01-15",
        "ora": "14:30",
        "durata": 60
    }
    """
    try:
        # Parse JSON input
        evento = json.loads(input)
        
        # Valida i campi richiesti
        required_fields = ['titolo', 'data', 'ora']
        for field in required_fields:
            if field not in evento:
                return f"Errore: campo '{field}' mancante"
        
        # Salva nella working memory
        if 'eventi' not in cat.working_memory:
            cat.working_memory['eventi'] = []
        
        cat.working_memory['eventi'].append(evento)
        
        return f"Evento '{evento['titolo']}' creato per il {evento['data']} alle {evento['ora']}"
        
    except json.JSONDecodeError:
        return "Errore: input deve essere un JSON valido"
    except Exception as e:
        return f"Errore nella creazione evento: {str(e)}"
```

### 2. Tool con Stato Persistente

```python
from cat.mad_hatter.decorators import tool
import json
import os

@tool
def gestisci_todo(input, cat):
    """Gestisce una lista TODO persistente.
    
    Comandi:
    - "aggiungi: fare la spesa"
    - "lista"
    - "completa: 1" (completa item numero 1)
    - "rimuovi: 2" (rimuove item numero 2)
    """
    todo_file = "cat/data/todo.json"
    
    # Carica TODO esistenti
    try:
        if os.path.exists(todo_file):
            with open(todo_file, 'r') as f:
                todos = json.load(f)
        else:
            todos = []
    except:
        todos = []
    
    # Parse comando
    parts = input.split(':', 1)
    comando = parts[0].strip().lower()
    
    if comando == "aggiungi" and len(parts) > 1:
        task = parts[1].strip()
        todos.append({"id": len(todos) + 1, "task": task, "completed": False})
        result = f"Aggiunto: {task}"
        
    elif comando == "lista":
        if not todos:
            result = "Lista TODO vuota"
        else:
            items = []
            for todo in todos:
                status = "✅" if todo["completed"] else "⏳"
                items.append(f"{todo['id']}. {status} {todo['task']}")
            result = "Lista TODO:\n" + "\n".join(items)
            
    elif comando == "completa" and len(parts) > 1:
        try:
            todo_id = int(parts[1].strip())
            for todo in todos:
                if todo["id"] == todo_id:
                    todo["completed"] = True
                    result = f"Completato: {todo['task']}"
                    break
            else:
                result = f"TODO {todo_id} non trovato"
        except ValueError:
            result = "Errore: specifica un numero valido"
            
    else:
        result = "Comandi: aggiungi:task, lista, completa:id"
    
    # Salva TODO
    try:
        with open(todo_file, 'w') as f:
            json.dump(todos, f, indent=2)
    except Exception as e:
        result += f"\nErrore nel salvataggio: {str(e)}"
    
    return result
```

## 🔧 Best Practices per Tools

### 1. **Documentazione Clara**
```python
@tool
def mio_tool(input, cat):
    """Descrizione chiara e concisa.
    
    Spiega:
    - Cosa fa il tool
    - Formato dell'input atteso
    - Esempi di utilizzo
    - Possibili errori
    """
```

### 2. **Gestione Errori Robusta**
```python
@tool
def tool_sicuro(input, cat):
    """Tool con gestione errori completa."""
    try:
        # Valida input
        if not input or input.strip() == "":
            return "Errore: input richiesto"
        
        # Logica principale
        result = process_input(input)
        
        return f"Successo: {result}"
        
    except ValueError as e:
        return f"Errore di validazione: {str(e)}"
    except Exception as e:
        cat.log.error(f"Errore in tool_sicuro: {str(e)}")
        return "Errore interno del tool"
```

### 3. **Performance e Limiti**
```python
@tool
def tool_performante(input, cat):
    """Tool ottimizzato per performance."""
    # Limita la dimensione dell'input
    if len(input) > 1000:
        return "Errore: input troppo lungo (max 1000 caratteri)"
    
    # Usa caching se possibile
    cache_key = f"tool_result_{hash(input)}"
    if cache_key in cat.working_memory:
        return cat.working_memory[cache_key]
    
    # Processa
    result = expensive_operation(input)
    
    # Salva in cache
    cat.working_memory[cache_key] = result
    
    return result
```

### 4. **Sicurezza**
```python
@tool
def tool_sicuro(input, cat):
    """Tool con controlli di sicurezza."""
    # Whitelist di caratteri permessi
    import re
    if not re.match(r'^[a-zA-Z0-9\s\.,!?-]+$', input):
        return "Errore: caratteri non permessi nell'input"
    
    # Evita eval() e exec()
    # Non eseguire mai codice dall'input utente
    
    # Limita accesso al filesystem
    if '../' in input or input.startswith('/'):
        return "Errore: percorso non permesso"
```

## 🎯 Prossimi Passi

Ora che sai creare Tools potenti:

1. **🧪 Prova gli esempi**: Copia e testa i tools di questa guida
2. **🔨 Crea il tuo tool**: Inizia con qualcosa di semplice
3. **🪝 Impara gli Hooks**: [05-plugin-hooks.md](05-plugin-hooks.md)

---

**Pronto per personalizzare il comportamento?** Vai al prossimo capitolo: [05-plugin-hooks.md](05-plugin-hooks.md) 🚀
