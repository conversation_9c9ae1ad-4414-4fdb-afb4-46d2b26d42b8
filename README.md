# 🐱 Stregatto - Guida Completa a Cheshire Cat AI

Questo progetto ti guiderà alla comprensione completa di **Cheshire Cat AI**, un framework AI pronto all'uso per creare assistenti intelligenti personalizzati.

## 🎯 Cosa Imparerai

- **Cos'è Cheshire Cat AI** e come funziona
- **Installazione e configurazione** del framework
- **Creazione di Plugin** con Tools, Hooks e Forms
- **Esempi pratici** progressivi dal semplice al complesso
- **Best practices** per lo sviluppo

## 📚 Struttura del Progetto

```
stregatto/
├── docs/                    # Documentazione completa in italiano
│   ├── 01-introduzione.md
│   ├── 02-installazione.md
│   ├── 03-concetti-base.md
│   ├── 04-plugin-tools.md
│   ├── 05-plugin-hooks.md
│   ├── 06-plugin-forms.md
│   └── 07-esempi-avanzati.md
├── examples/               # Esempi di plugin progressivi
│   ├── 01-primo-plugin/
│   ├── 02-tool-semplice/
│   ├── 03-hook-personalizzazione/
│   ├── 04-form-interattivo/
│   └── 05-plugin-completo/
├── plugins/               # Plugin pronti all'uso
│   ├── template-base/
│   ├── calcolatrice/
│   ├── meteo/
│   └── task-manager/
├── scripts/               # Script di utilità
│   ├── setup.sh
│   ├── install-cat.sh
│   └── create-plugin.py
└── README.md             # Questa guida
```

## 🚀 Quick Start

1. **Leggi la guida introduttiva**: `docs/01-introduzione.md`
2. **Segui l'installazione**: `docs/02-installazione.md`
3. **Prova gli esempi**: Inizia da `examples/01-primo-plugin/`
4. **Crea il tuo plugin**: Usa i template in `plugins/`

## 📖 Percorso di Apprendimento

### Livello Principiante
1. Leggi `docs/01-introduzione.md` per capire cos'è Cheshire Cat
2. Segui `docs/02-installazione.md` per installare tutto
3. Prova `examples/01-primo-plugin/` per il tuo primo plugin

### Livello Intermedio
4. Studia `docs/04-plugin-tools.md` e prova `examples/02-tool-semplice/`
5. Impara gli hooks con `docs/05-plugin-hooks.md` e `examples/03-hook-personalizzazione/`
6. Scopri i forms con `docs/06-plugin-forms.md` e `examples/04-form-interattivo/`

### Livello Avanzato
7. Crea plugin complessi con `examples/05-plugin-completo/`
8. Studia `docs/07-esempi-avanzati.md` per tecniche avanzate
9. Contribuisci alla community!

## 🛠️ Installazione Rapida

```bash
# Clona il repository di Cheshire Cat
git clone https://github.com/cheshire-cat-ai/core.git cheshire-cat
cd cheshire-cat

# Avvia con Docker (raccomandato)
docker-compose up

# Oppure installa manualmente
pip install -r requirements.txt
python main.py
```

Il Cat sarà disponibile su `http://localhost:1865`

## 🔗 Link Utili

- [Documentazione Ufficiale](https://cheshire-cat-ai.github.io/docs/)
- [Repository GitHub](https://github.com/cheshire-cat-ai/core)
- [Discord Community](https://discord.gg/bHX5sNFCYU)
- [Plugin Registry](https://github.com/cheshire-cat-ai/plugins)

## 🤝 Come Contribuire

1. Fai fork del progetto
2. Crea un branch per la tua feature (`git checkout -b feature/AmazingFeature`)
3. Committa le tue modifiche (`git commit -m 'Add some AmazingFeature'`)
4. Pusha sul branch (`git push origin feature/AmazingFeature`)
5. Apri una Pull Request

## 📝 Licenza

Questo progetto è distribuito sotto licenza MIT. Vedi `LICENSE` per maggiori informazioni.

---

**Inizia subito**: Apri `docs/01-introduzione.md` per iniziare il tuo viaggio con Cheshire Cat AI! 🚀
